# Project Settings
PROJECT_NAME=FastAPI Backend
VERSION=1.0.0
DESCRIPTION=A FastAPI backend application
API_V1_STR=/api/v1

# Security
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Database - 云服务器MySQL配置
DATABASE_URL=mysql+pymysql://lptcgl:<EMAIL>:3306/lptcgl

# 其他数据库配置示例
# For local MySQL
# DATABASE_URL=mysql+pymysql://username:password@localhost:3306/dbname
# For SQLite (development)
# DATABASE_URL=sqlite:///./app.db
# For PostgreSQL
# DATABASE_URL=postgresql://username:password@localhost/dbname

# CORS
BACKEND_CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]

# Environment
ENVIRONMENT=development

# Email (optional)
SMTP_TLS=True
SMTP_PORT=587
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-password

# Redis (optional)
REDIS_URL=redis://localhost:6379
