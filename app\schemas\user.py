from typing import Optional
from pydantic import BaseModel, validator, computed_field
from datetime import datetime
from enum import Enum
import re


class UserRole(str, Enum):
    """用户角色枚举"""
    SUPER_ADMIN = "super_admin"      # 超级管理员
    ADMIN = "admin"                  # 普通管理员
    BREEDER = "breeder"              # 饲养员
    SALESPERSON = "salesperson"      # 销售员
    USER = "user"                    # 普通用户


class UserRoleDisplay(str, Enum):
    """用户角色显示名称"""
    SUPER_ADMIN = "超级管理员"
    ADMIN = "普通管理员"
    BREEDER = "饲养员"
    SALESPERSON = "销售员"
    USER = "普通用户"


def get_role_display_name(role: UserRole) -> str:
    """获取角色显示名称"""
    role_map = {
        UserRole.SUPER_ADMIN: "超级管理员",
        UserRole.ADMIN: "普通管理员",
        UserRole.BREEDER: "饲养员",
        UserRole.SALESPERSON: "销售员",
        UserRole.USER: "普通用户",
    }
    return role_map.get(role, "普通用户")


class UserBase(BaseModel):
    phone: str
    username: str
    role: Optional[UserRole] = UserRole.USER
    is_active: Optional[bool] = True
    is_superuser: Optional[bool] = False

    @validator('phone')
    def validate_phone(cls, v):
        # 验证手机号格式（中国大陆手机号）
        if not re.match(r'^1[3-9]\d{9}$', v):
            raise ValueError('手机号格式不正确，请输入11位有效的中国大陆手机号')
        return v


class UserCreate(UserBase):
    password: str
    role: Optional[UserRole] = UserRole.USER


class UserUpdate(BaseModel):
    phone: Optional[str] = None
    username: Optional[str] = None
    password: Optional[str] = None
    is_active: Optional[bool] = None
    is_superuser: Optional[bool] = None

    @validator('phone')
    def validate_phone(cls, v):
        if v is not None:
            if not re.match(r'^1[3-9]\d{9}$', v):
                raise ValueError('手机号格式不正确，请输入11位有效的中国大陆手机号')
        return v


class UserInDBBase(UserBase):
    id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class User(UserInDBBase):
    pass


class UserInfo(BaseModel):
    """优化的用户信息返回模型（只返回必要字段）"""
    id: int
    phone: str
    username: str
    role: UserRole
    role_display: str
    is_active: bool
    is_superuser: bool

    class Config:
        from_attributes = True


class UserInDB(UserInDBBase):
    hashed_password: str


class Token(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str
    expires_in: int  # 访问token的有效期（秒）


class TokenPayload(BaseModel):
    sub: Optional[int] = None


class LoginRequest(BaseModel):
    phone: str
    password: str
    captcha_id: Optional[str] = None
    captcha_code: Optional[str] = None

    @validator('phone')
    def validate_phone(cls, v):
        if not re.match(r'^1[3-9]\d{9}$', v):
            raise ValueError('手机号格式不正确，请输入11位有效的中国大陆手机号')
        return v


class CaptchaResponse(BaseModel):
    captcha_id: str
    captcha_image: str  # base64编码的图片
    expires_in: int = 300  # 5分钟过期


class LoginAttemptInfo(BaseModel):
    remaining_attempts: int
    is_locked: bool
    locked_until: Optional[datetime] = None
    requires_captcha: bool


class LogoutResponse(BaseModel):
    message: str
    logged_out_at: datetime
