"""
销售订单统计API端点
"""

from typing import Any, Dict, List, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_
from app.api import deps
from app.db.database import get_db
from app.db.models import User, Order, OrderItem, Customer, RabbitType, OrderStatus
from app.core.permissions import require_permission, Permission, has_permission

router = APIRouter()


@router.get("/orders/summary")
def get_orders_summary(
    db: Session = Depends(get_db),
    start_date: Optional[str] = Query(None, description="开始日期 YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期 YYYY-MM-DD"),
    salesperson_id: Optional[int] = Query(None, description="销售员ID"),
    current_user: User = Depends(require_permission(Permission.ORDER_READ)),
) -> Any:
    """
    获取订单统计摘要
    """
    # 构建查询条件
    query_conditions = []
    
    # 日期范围过滤
    if start_date:
        start_datetime = datetime.strptime(start_date, "%Y-%m-%d")
        query_conditions.append(Order.created_at >= start_datetime)
    
    if end_date:
        end_datetime = datetime.strptime(end_date, "%Y-%m-%d") + timedelta(days=1)
        query_conditions.append(Order.created_at < end_datetime)
    
    # 销售员过滤
    if salesperson_id:
        query_conditions.append(Order.salesperson_id == salesperson_id)
    elif not has_permission(current_user, Permission.ORDER_READ_ALL):
        # 如果用户没有查看所有订单的权限，只能看自己的
        query_conditions.append(Order.salesperson_id == current_user.id)
    
    # 基础查询
    base_query = db.query(Order)
    if query_conditions:
        base_query = base_query.filter(and_(*query_conditions))
    
    # 统计数据
    total_orders = base_query.count()
    
    # 按状态统计
    status_stats = db.query(
        Order.status,
        func.count(Order.id).label('count'),
        func.sum(Order.total_price).label('total_amount'),
        func.sum(Order.amount_paid).label('paid_amount')
    ).filter(and_(*query_conditions) if query_conditions else True).group_by(Order.status).all()
    
    # 总金额统计
    amount_stats = base_query.with_entities(
        func.sum(Order.total_price).label('total_amount'),
        func.sum(Order.amount_paid).label('paid_amount'),
        func.sum(Order.total_price - Order.amount_paid).label('unpaid_amount')
    ).first()
    
    # 按销售员统计（如果有权限查看所有订单）
    salesperson_stats = []
    if has_permission(current_user, Permission.ORDER_READ_ALL):
        salesperson_stats = db.query(
            User.id,
            User.username,
            func.count(Order.id).label('order_count'),
            func.sum(Order.total_price).label('total_amount'),
            func.sum(Order.amount_paid).label('paid_amount')
        ).join(Order, User.id == Order.salesperson_id).filter(
            and_(*query_conditions) if query_conditions else True
        ).group_by(User.id, User.username).all()
    
    # 格式化状态统计
    status_summary = {}
    for stat in status_stats:
        status_summary[stat.status.value] = {
            "count": stat.count,
            "total_amount": float(stat.total_amount or 0),
            "paid_amount": float(stat.paid_amount or 0)
        }
    
    # 格式化销售员统计
    salesperson_summary = []
    for stat in salesperson_stats:
        salesperson_summary.append({
            "salesperson_id": stat.id,
            "salesperson_name": stat.username,
            "order_count": stat.order_count,
            "total_amount": float(stat.total_amount or 0),
            "paid_amount": float(stat.paid_amount or 0),
            "unpaid_amount": float((stat.total_amount or 0) - (stat.paid_amount or 0))
        })
    
    return {
        "summary": {
            "total_orders": total_orders,
            "total_amount": float(amount_stats.total_amount or 0),
            "paid_amount": float(amount_stats.paid_amount or 0),
            "unpaid_amount": float(amount_stats.unpaid_amount or 0)
        },
        "status_breakdown": status_summary,
        "salesperson_breakdown": salesperson_summary,
        "period": {
            "start_date": start_date,
            "end_date": end_date
        }
    }


@router.get("/orders/trends")
def get_orders_trends(
    db: Session = Depends(get_db),
    days: int = Query(30, description="统计天数"),
    salesperson_id: Optional[int] = Query(None, description="销售员ID"),
    current_user: User = Depends(require_permission(Permission.ORDER_READ)),
) -> Any:
    """
    获取订单趋势数据（按日统计）
    """
    # 计算日期范围
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=days-1)
    
    # 构建查询条件
    query_conditions = [
        func.date(Order.created_at) >= start_date,
        func.date(Order.created_at) <= end_date
    ]
    
    # 销售员过滤
    if salesperson_id:
        query_conditions.append(Order.salesperson_id == salesperson_id)
    elif not has_permission(current_user, Permission.ORDER_READ_ALL):
        query_conditions.append(Order.salesperson_id == current_user.id)
    
    # 按日期统计
    daily_stats = db.query(
        func.date(Order.created_at).label('date'),
        func.count(Order.id).label('order_count'),
        func.sum(Order.total_price).label('total_amount'),
        func.sum(Order.amount_paid).label('paid_amount')
    ).filter(and_(*query_conditions)).group_by(
        func.date(Order.created_at)
    ).order_by(func.date(Order.created_at)).all()
    
    # 格式化数据
    trends = []
    for stat in daily_stats:
        trends.append({
            "date": stat.date.strftime("%Y-%m-%d"),
            "order_count": stat.order_count,
            "total_amount": float(stat.total_amount or 0),
            "paid_amount": float(stat.paid_amount or 0),
            "unpaid_amount": float((stat.total_amount or 0) - (stat.paid_amount or 0))
        })
    
    return {
        "trends": trends,
        "period": {
            "start_date": start_date.strftime("%Y-%m-%d"),
            "end_date": end_date.strftime("%Y-%m-%d"),
            "days": days
        }
    }


@router.get("/products/ranking")
def get_product_ranking(
    db: Session = Depends(get_db),
    start_date: Optional[str] = Query(None, description="开始日期 YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期 YYYY-MM-DD"),
    limit: int = Query(10, description="返回数量"),
    current_user: User = Depends(require_permission(Permission.ORDER_READ)),
) -> Any:
    """
    获取产品销售排行榜
    """
    # 构建查询条件
    query_conditions = []
    
    # 日期范围过滤
    if start_date:
        start_datetime = datetime.strptime(start_date, "%Y-%m-%d")
        query_conditions.append(Order.created_at >= start_datetime)
    
    if end_date:
        end_datetime = datetime.strptime(end_date, "%Y-%m-%d") + timedelta(days=1)
        query_conditions.append(Order.created_at < end_datetime)
    
    # 权限过滤
    if not has_permission(current_user, Permission.ORDER_READ_ALL):
        query_conditions.append(Order.salesperson_id == current_user.id)
    
    # 产品销售统计
    product_stats = db.query(
        RabbitType.id,
        RabbitType.name,
        func.sum(OrderItem.quantity).label('total_quantity'),
        func.sum(OrderItem.total_price).label('total_amount'),
        func.count(OrderItem.id).label('order_count')
    ).join(OrderItem, RabbitType.id == OrderItem.rabbit_type_id
    ).join(Order, OrderItem.order_id == Order.id)
    
    if query_conditions:
        product_stats = product_stats.filter(and_(*query_conditions))
    
    product_stats = product_stats.group_by(
        RabbitType.id, RabbitType.name
    ).order_by(
        func.sum(OrderItem.total_price).desc()
    ).limit(limit).all()
    
    # 格式化数据
    ranking = []
    for i, stat in enumerate(product_stats, 1):
        ranking.append({
            "rank": i,
            "product_id": stat.id,
            "product_name": stat.name,
            "total_quantity": float(stat.total_quantity or 0),
            "total_amount": float(stat.total_amount or 0),
            "order_count": stat.order_count
        })
    
    return {
        "ranking": ranking,
        "period": {
            "start_date": start_date,
            "end_date": end_date
        }
    }


@router.get("/customers/ranking")
def get_customer_ranking(
    db: Session = Depends(get_db),
    start_date: Optional[str] = Query(None, description="开始日期 YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期 YYYY-MM-DD"),
    limit: int = Query(10, description="返回数量"),
    current_user: User = Depends(require_permission(Permission.ORDER_READ)),
) -> Any:
    """
    获取客户消费排行榜
    """
    # 构建查询条件
    query_conditions = []
    
    # 日期范围过滤
    if start_date:
        start_datetime = datetime.strptime(start_date, "%Y-%m-%d")
        query_conditions.append(Order.created_at >= start_datetime)
    
    if end_date:
        end_datetime = datetime.strptime(end_date, "%Y-%m-%d") + timedelta(days=1)
        query_conditions.append(Order.created_at < end_datetime)
    
    # 权限过滤
    if not has_permission(current_user, Permission.ORDER_READ_ALL):
        query_conditions.append(Order.salesperson_id == current_user.id)
    
    # 客户消费统计
    customer_stats = db.query(
        Customer.id,
        Customer.name,
        Customer.phone,
        func.count(Order.id).label('order_count'),
        func.sum(Order.total_price).label('total_amount'),
        func.sum(Order.amount_paid).label('paid_amount')
    ).join(Order, Customer.id == Order.customer_id)
    
    if query_conditions:
        customer_stats = customer_stats.filter(and_(*query_conditions))
    
    customer_stats = customer_stats.group_by(
        Customer.id, Customer.name, Customer.phone
    ).order_by(
        func.sum(Order.total_price).desc()
    ).limit(limit).all()
    
    # 格式化数据
    ranking = []
    for i, stat in enumerate(customer_stats, 1):
        ranking.append({
            "rank": i,
            "customer_id": stat.id,
            "customer_name": stat.name,
            "customer_phone": stat.phone,
            "order_count": stat.order_count,
            "total_amount": float(stat.total_amount or 0),
            "paid_amount": float(stat.paid_amount or 0),
            "unpaid_amount": float((stat.total_amount or 0) - (stat.paid_amount or 0))
        })
    
    return {
        "ranking": ranking,
        "period": {
            "start_date": start_date,
            "end_date": end_date
        }
    }
