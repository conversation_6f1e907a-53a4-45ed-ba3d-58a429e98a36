"""
MySQL特定配置和工具函数
"""

from sqlalchemy import event
from sqlalchemy.engine import Engine
from sqlalchemy.pool import Pool
import pymysql


def configure_mysql_engine(engine: Engine):
    """
    配置MySQL引擎的特定设置
    """
    
    @event.listens_for(engine, "connect")
    def set_mysql_pragma(dbapi_connection, connection_record):
        """设置MySQL连接参数"""
        if hasattr(dbapi_connection, 'cursor'):
            cursor = dbapi_connection.cursor()
            # 设置字符集
            cursor.execute("SET NAMES utf8mb4")
            # 设置时区
            cursor.execute("SET time_zone = '+00:00'")
            # 设置SQL模式
            cursor.execute("SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'")
            cursor.close()
    
    @event.listens_for(Pool, "connect")
    def set_mysql_encoding(dbapi_connection, connection_record):
        """设置MySQL连接编码"""
        if isinstance(dbapi_connection, pymysql.Connection):
            dbapi_connection.set_charset('utf8mb4')


def get_mysql_url_params():
    """
    获取MySQL连接URL的推荐参数
    """
    return {
        'charset': 'utf8mb4',
        'use_unicode': True,
        'autocommit': False,
    }


def create_mysql_engine_args():
    """
    创建MySQL引擎的推荐参数
    """
    return {
        'pool_pre_ping': True,  # 连接前检查连接是否有效
        'pool_recycle': 3600,   # 1小时后回收连接
        'pool_size': 10,        # 连接池大小
        'max_overflow': 20,     # 最大溢出连接数
        'echo': False,          # 是否打印SQL语句
    }
