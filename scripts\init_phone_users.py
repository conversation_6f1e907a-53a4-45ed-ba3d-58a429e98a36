#!/usr/bin/env python3
"""
使用手机号的用户数据库初始化脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.db.cloud_config import CloudDatabaseConfig
from app.crud.crud_user import user
from app.schemas.user import UserCreate
from app.db.database import SessionLocal, Base, engine
from app.db.models import User, UserRole


def drop_and_recreate_tables():
    """删除并重新创建表"""
    print("🗄️ 删除旧表并重新创建...")
    
    try:
        # 删除所有表
        Base.metadata.drop_all(bind=engine)
        print("✅ 旧表删除成功")
        
        # 重新创建表
        Base.metadata.create_all(bind=engine)
        print("✅ 新表创建成功")
        return True
        
    except Exception as e:
        print(f"❌ 表操作失败: {e}")
        return False


def create_phone_users():
    """创建使用手机号的用户"""
    print("👤 创建手机号用户...")
    
    db = SessionLocal()
    try:
        # 创建管理员用户
        admin_user = UserCreate(
            phone="13800138000",
            username="admin",
            password="admin123",
            role="super_admin",
            is_superuser=True,
            is_active=True
        )
        
        created_admin = user.create(db, obj_in=admin_user)
        print(f"✅ 管理员用户创建成功")
        print(f"   手机号: {created_admin.phone}")
        print(f"   用户名: {created_admin.username}")
        print(f"   密码: admin123")
        
        # 创建普通管理员用户
        demo_user = UserCreate(
            phone="13900139000",
            username="demo",
            password="demo123",
            role="admin",
            is_superuser=False,
            is_active=True
        )
        
        created_demo = user.create(db, obj_in=demo_user)
        print(f"✅ 演示用户创建成功")
        print(f"   手机号: {created_demo.phone}")
        print(f"   用户名: {created_demo.username}")
        print(f"   密码: demo123")
        
        # 创建测试用户
        test_user = UserCreate(
            phone="15000150000",
            username="test",
            password="test123",
            role="user",
            is_superuser=False,
            is_active=True
        )

        created_test = user.create(db, obj_in=test_user)
        print(f"✅ 测试用户创建成功")
        print(f"   手机号: {created_test.phone}")
        print(f"   用户名: {created_test.username}")
        print(f"   角色: 普通用户")
        print(f"   密码: test123")

        # 创建饲养员用户
        breeder_user = UserCreate(
            phone="13700137000",
            username="breeder01",
            password="breeder123",
            role="breeder",
            is_superuser=False,
            is_active=True
        )

        created_breeder = user.create(db, obj_in=breeder_user)
        print(f"✅ 饲养员用户创建成功")
        print(f"   手机号: {created_breeder.phone}")
        print(f"   用户名: {created_breeder.username}")
        print(f"   角色: 饲养员")
        print(f"   密码: breeder123")

        # 创建销售员用户
        sales_user = UserCreate(
            phone="13600136000",
            username="sales01",
            password="sales123",
            role="salesperson",
            is_superuser=False,
            is_active=True
        )

        created_sales = user.create(db, obj_in=sales_user)
        print(f"✅ 销售员用户创建成功")
        print(f"   手机号: {created_sales.phone}")
        print(f"   用户名: {created_sales.username}")
        print(f"   角色: 销售员")
        print(f"   密码: sales123")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建用户失败: {e}")
        return False
    finally:
        db.close()


def test_phone_login():
    """测试手机号登录"""
    print("🔐 测试手机号登录...")
    
    db = SessionLocal()
    try:
        # 测试管理员登录
        admin = user.authenticate(db, phone="13800138000", password="admin123")
        if admin:
            print(f"✅ 管理员登录测试成功: {admin.username}")
        else:
            print("❌ 管理员登录测试失败")
            return False
        
        # 测试普通用户登录
        demo = user.authenticate(db, phone="13900139000", password="demo123")
        if demo:
            print(f"✅ 演示用户登录测试成功: {demo.username}")
        else:
            print("❌ 演示用户登录测试失败")
            return False
        
        # 测试错误密码
        wrong_auth = user.authenticate(db, phone="13800138000", password="wrong")
        if not wrong_auth:
            print("✅ 错误密码验证测试成功")
        else:
            print("❌ 错误密码验证测试失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 登录测试失败: {e}")
        return False
    finally:
        db.close()


def main():
    """主函数"""
    print("🚀 手机号用户系统初始化")
    print("=" * 50)
    print(f"📍 数据库地址: {CloudDatabaseConfig.HOST}:{CloudDatabaseConfig.PORT}")
    print(f"📍 数据库名称: {CloudDatabaseConfig.DATABASE}")
    print("=" * 50)
    
    # 1. 测试连接
    success, message = CloudDatabaseConfig.test_connection()
    if not success:
        print(f"❌ 数据库连接失败: {message}")
        return False
    print(f"✅ 数据库连接成功")
    
    # 2. 重新创建表
    if not drop_and_recreate_tables():
        return False
    
    # 3. 创建手机号用户
    if not create_phone_users():
        return False
    
    # 4. 测试登录
    if not test_phone_login():
        return False
    
    print("\n🎉 手机号用户系统初始化完成！")
    print("\n📋 用户账户信息:")
    print("   管理员账户:")
    print("     手机号: 13800138000")
    print("     用户名: admin")
    print("     密码: admin123")
    print("   演示账户:")
    print("     手机号: 13900139000")
    print("     用户名: demo")
    print("     密码: demo123")
    print("   测试账户:")
    print("     手机号: 15000150000")
    print("     用户名: test")
    print("     密码: test123")
    print("\n🌐 下一步:")
    print("   1. 重启应用: python main.py")
    print("   2. 访问 API 文档: http://localhost:8000/docs")
    print("   3. 使用手机号登录测试")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
