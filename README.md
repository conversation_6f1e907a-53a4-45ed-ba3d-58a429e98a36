# FastAPI 手机号登录系统

基于FastAPI的手机号登录系统，支持验证码防护和完整的CORS跨域配置。

## ✨ 核心功能

- 🔐 **手机号登录** - 中国大陆11位手机号登录
- 🛡️ **验证码防护** - 图形验证码 + 登录失败锁定
- 🚪 **退出登录** - 真正的退出登录，token黑名单机制
- 🌐 **CORS支持** - 支持9000、9001等多个前端端口
- 🔑 **JWT认证** - 安全的token认证机制
- 📱 **完整API** - 用户管理、认证等完整接口
- 🐇 **兔子类型管理** - 兔子分类管理（满月兔、公种兔、母种兔等）
- 👤 **客户管理** - 客户信息录入和管理系统
- 🛒 **销售订单管理** - 完整的销售记录系统，支持多种兔子类型、多单位、部分付款

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 初始化数据库
```bash
python scripts/init_phone_users.py
```

### 3. 启动服务
```bash
python main.py
```

### 4. 访问服务
- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health
- **CORS配置**: http://localhost:8000/cors-info
- **前端演示**: frontend-examples/customer-rabbit-demo.html

## 📚 文档

- **完整API文档**: [API_DOCUMENTATION.md](./API_DOCUMENTATION.md) - 包含所有API接口的详细说明
- **项目状态**: [STATUS.md](./STATUS.md) - 当前功能状态和开发进度
- **前端示例**: [frontend-examples/](./frontend-examples/) - 完整的前端集成示例

## 🧪 测试功能

```bash
# 测试登录功能
python scripts/test_phone_login.py

# 测试验证码功能
python scripts/test_captcha_login.py

# 测试退出登录功能
python scripts/test_logout.py

# 测试CORS功能
python scripts/test_cors.py

# 调试CORS问题
python scripts/cors_debug.py http://localhost:9000
```

## 🔐 测试账户

| 角色 | 手机号 | 用户名 | 密码 |
|------|--------|--------|------|
| 管理员 | 13800138000 | admin | admin123 |
| 演示用户 | 13900139000 | demo | demo123 |
| 测试用户 | 15000150000 | test | test123 |

## 🌐 CORS支持

支持以下前端端口的跨域访问：
- **React**: http://localhost:3000
- **Vue.js**: http://localhost:8080
- **Vite**: http://localhost:5173
- **Angular**: http://localhost:4200
- **自定义**: http://localhost:9000, http://localhost:9001

## 📖 相关文档

- **API使用指南**: `API_USAGE.md`
- **CORS详细指南**: `CORS_GUIDE.md`
- **验证码指南**: `CAPTCHA_GUIDE.md`
- **前端快速接入**: `FRONTEND_QUICK_START.md`
