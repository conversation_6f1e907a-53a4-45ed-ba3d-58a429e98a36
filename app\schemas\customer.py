"""
客户相关的Pydantic模型
"""

from typing import Optional
from datetime import datetime
from pydantic import BaseModel, validator
import re


class CustomerBase(BaseModel):
    """客户基础模型"""
    name: str
    phone: str
    address: Optional[str] = None
    remark: Optional[str] = None

    @validator('name')
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('姓名或单位名称不能为空')
        if len(v.strip()) > 100:
            raise ValueError('姓名或单位名称长度不能超过100个字符')
        return v.strip()

    @validator('phone')
    def validate_phone(cls, v):
        if not v or not v.strip():
            raise ValueError('联系方式不能为空')
        # 支持手机号和固定电话
        phone_clean = v.strip()
        if len(phone_clean) > 20:
            raise ValueError('联系方式长度不能超过20个字符')
        return phone_clean

    @validator('address')
    def validate_address(cls, v):
        if v is not None and len(v.strip()) > 255:
            raise ValueError('地址长度不能超过255个字符')
        return v.strip() if v else None

    @validator('remark')
    def validate_remark(cls, v):
        if v is not None and len(v.strip()) > 1000:
            raise ValueError('备注长度不能超过1000个字符')
        return v.strip() if v else None


class CustomerCreate(CustomerBase):
    """创建客户模型"""
    pass


class CustomerUpdate(BaseModel):
    """更新客户模型"""
    name: Optional[str] = None
    phone: Optional[str] = None
    address: Optional[str] = None
    remark: Optional[str] = None

    @validator('name')
    def validate_name(cls, v):
        if v is not None:
            if not v.strip():
                raise ValueError('姓名或单位名称不能为空')
            if len(v.strip()) > 100:
                raise ValueError('姓名或单位名称长度不能超过100个字符')
            return v.strip()
        return v

    @validator('phone')
    def validate_phone(cls, v):
        if v is not None:
            if not v.strip():
                raise ValueError('联系方式不能为空')
            phone_clean = v.strip()
            if len(phone_clean) > 20:
                raise ValueError('联系方式长度不能超过20个字符')
            return phone_clean
        return v

    @validator('address')
    def validate_address(cls, v):
        if v is not None and len(v.strip()) > 255:
            raise ValueError('地址长度不能超过255个字符')
        return v.strip() if v else None

    @validator('remark')
    def validate_remark(cls, v):
        if v is not None and len(v.strip()) > 1000:
            raise ValueError('备注长度不能超过1000个字符')
        return v.strip() if v else None


class CustomerInDBBase(CustomerBase):
    """数据库中的客户基础模型"""
    id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class Customer(CustomerBase):
    """客户模型（用于API响应）- 简化版，不包含时间戳"""
    id: int

    class Config:
        from_attributes = True


class CustomerInDB(CustomerInDBBase):
    """数据库中的客户模型"""
    pass
