#!/usr/bin/env python3
"""
清理项目中的临时文件和缓存
"""

import os
import shutil
import sys

def cleanup_pycache(root_dir="."):
    """清理__pycache__目录"""
    removed_count = 0
    
    for root, dirs, files in os.walk(root_dir):
        if "__pycache__" in dirs:
            pycache_path = os.path.join(root, "__pycache__")
            try:
                shutil.rmtree(pycache_path)
                print(f"✅ 删除: {pycache_path}")
                removed_count += 1
            except Exception as e:
                print(f"❌ 删除失败: {pycache_path} - {e}")
    
    return removed_count

def cleanup_pyc_files(root_dir="."):
    """清理.pyc文件"""
    removed_count = 0
    
    for root, dirs, files in os.walk(root_dir):
        for file in files:
            if file.endswith('.pyc'):
                pyc_path = os.path.join(root, file)
                try:
                    os.remove(pyc_path)
                    print(f"✅ 删除: {pyc_path}")
                    removed_count += 1
                except Exception as e:
                    print(f"❌ 删除失败: {pyc_path} - {e}")
    
    return removed_count

def main():
    print("🧹 开始清理项目临时文件...")
    
    # 清理__pycache__目录
    print("\n📁 清理__pycache__目录...")
    pycache_count = cleanup_pycache()
    
    # 清理.pyc文件
    print("\n📄 清理.pyc文件...")
    pyc_count = cleanup_pyc_files()
    
    print(f"\n🎉 清理完成！")
    print(f"   - 删除了 {pycache_count} 个__pycache__目录")
    print(f"   - 删除了 {pyc_count} 个.pyc文件")

if __name__ == "__main__":
    main()
