#!/usr/bin/env python3
"""
测试新的token系统
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000/api/v1"

def test_token_system():
    print("🧪 测试新的Token系统...")
    
    # 1. 登录获取token
    print("\n1. 登录获取token...")
    login_data = {
        "phone": "13800138000",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    if response.status_code == 200:
        token_data = response.json()
        print("✅ 登录成功")
        print(f"   - Access Token: {token_data['access_token'][:20]}...")
        print(f"   - Refresh Token: {token_data['refresh_token'][:20]}...")
        print(f"   - Token Type: {token_data['token_type']}")
        print(f"   - Expires In: {token_data['expires_in']} 秒 ({token_data['expires_in']//3600} 小时)")
        
        access_token = token_data['access_token']
        refresh_token = token_data['refresh_token']
    else:
        print(f"❌ 登录失败: {response.text}")
        return
    
    # 2. 使用access token访问受保护的资源
    print("\n2. 使用access token访问受保护资源...")
    headers = {"Authorization": f"Bearer {access_token}"}
    
    response = requests.get(f"{BASE_URL}/users/me", headers=headers)
    if response.status_code == 200:
        user_info = response.json()
        print(f"✅ 访问成功: {user_info['username']} ({user_info['phone']})")
    else:
        print(f"❌ 访问失败: {response.text}")
    
    # 3. 测试刷新token
    print("\n3. 测试刷新token...")
    refresh_data = {"refresh_token": refresh_token}
    
    response = requests.post(f"{BASE_URL}/auth/refresh", json=refresh_data)
    if response.status_code == 200:
        new_token_data = response.json()
        print("✅ 刷新token成功")
        print(f"   - 新的Access Token: {new_token_data['access_token'][:20]}...")
        print(f"   - 新的Refresh Token: {new_token_data['refresh_token'][:20]}...")
        print(f"   - 有效期: {new_token_data['expires_in']} 秒")
        
        new_access_token = new_token_data['access_token']
    else:
        print(f"❌ 刷新token失败: {response.text}")
        return
    
    # 4. 使用新的access token访问资源
    print("\n4. 使用新的access token访问资源...")
    new_headers = {"Authorization": f"Bearer {new_access_token}"}
    
    response = requests.get(f"{BASE_URL}/users/me", headers=new_headers)
    if response.status_code == 200:
        user_info = response.json()
        print(f"✅ 新token访问成功: {user_info['username']}")
    else:
        print(f"❌ 新token访问失败: {response.text}")
    
    # 5. 测试token有效期信息
    print("\n5. Token有效期信息...")
    print(f"   - Access Token有效期: {token_data['expires_in']} 秒 = {token_data['expires_in']//60} 分钟")
    print(f"   - 这意味着用户可以连续工作 {token_data['expires_in']//3600} 小时而无需重新登录")
    print(f"   - Refresh Token有效期: 7天")
    print(f"   - 用户可以在7天内使用refresh token获取新的access token")
    
    # 6. 测试退出登录
    print("\n6. 测试退出登录...")
    response = requests.post(f"{BASE_URL}/auth/logout", headers=new_headers)
    if response.status_code == 200:
        logout_info = response.json()
        print(f"✅ 退出登录成功: {logout_info['message']}")
    else:
        print(f"❌ 退出登录失败: {response.text}")
    
    # 7. 验证退出后token是否失效
    print("\n7. 验证退出后token是否失效...")
    response = requests.get(f"{BASE_URL}/users/me", headers=new_headers)
    if response.status_code == 401:
        print("✅ 退出后token已失效，安全性良好")
    else:
        print(f"❌ 退出后token仍然有效，存在安全问题: {response.status_code}")
    
    print("\n🎉 Token系统测试完成！")

def show_token_config():
    """显示token配置信息"""
    print("📋 当前Token配置:")
    print("   - Access Token有效期: 8小时 (480分钟)")
    print("   - Refresh Token有效期: 7天")
    print("   - 适用场景: 日常办公使用")
    print("   - 安全特性: 支持token刷新、退出登录黑名单机制")

if __name__ == "__main__":
    show_token_config()
    test_token_system()
