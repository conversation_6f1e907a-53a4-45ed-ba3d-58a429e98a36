#!/usr/bin/env python3
"""
测试API响应
"""

import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_api():
    print("🔍 测试API响应...")
    
    # 1. 登录获取token
    print("1. 登录...")
    login_data = {"phone": "13800138000", "password": "admin123"}
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    
    if response.status_code == 200:
        token_data = response.json()
        headers = {"Authorization": f"Bearer {token_data['access_token']}"}
        print("✅ 登录成功")
    else:
        print(f"❌ 登录失败: {response.text}")
        return
    
    # 2. 测试删除有关联的兔子类型
    print("\n2. 测试删除兔子类型ID=5...")
    response = requests.delete(f"{BASE_URL}/rabbit-types/5", headers=headers)
    
    print(f"状态码: {response.status_code}")
    print(f"Content-Type: {response.headers.get('content-type', 'unknown')}")
    print(f"响应内容: {response.text}")
    
    if response.status_code == 400:
        try:
            error_data = response.json()
            print(f"✅ 错误详情: {error_data.get('detail', '无detail字段')}")
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
    
    # 3. 测试404错误
    print("\n3. 测试404错误...")
    response = requests.delete(f"{BASE_URL}/rabbit-types/99999", headers=headers)
    print(f"状态码: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    if response.status_code == 404:
        try:
            error_data = response.json()
            print(f"✅ 404错误详情: {error_data.get('detail', '无detail字段')}")
        except json.JSONDecodeError:
            print("❌ 404响应JSON解析失败")

if __name__ == "__main__":
    test_api()
