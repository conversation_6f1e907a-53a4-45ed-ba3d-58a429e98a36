from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from app.core.config import settings

# 检查是否使用云数据库
def is_cloud_database():
    """检查是否使用云数据库"""
    return "lp.beiluling.cn" in settings.DATABASE_URL

# 根据数据库类型配置引擎参数
engine_args = {}
if "mysql" in settings.DATABASE_URL:
    if is_cloud_database():
        # 使用云数据库配置
        from app.db.cloud_config import CloudDatabaseConfig
        engine_args = CloudDatabaseConfig.get_engine_config()
    else:
        # 使用本地MySQL配置
        from app.db.mysql_config import create_mysql_engine_args
        engine_args = create_mysql_engine_args()

engine = create_engine(settings.DATABASE_URL, **engine_args)

# 如果是MySQL，应用特殊配置
if "mysql" in settings.DATABASE_URL and not is_cloud_database():
    from app.db.mysql_config import configure_mysql_engine
    configure_mysql_engine(engine)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()


def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
