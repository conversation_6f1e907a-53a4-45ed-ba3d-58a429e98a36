<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>销售订单管理 - 演示页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .success {
            color: #28a745;
            background-color: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .order-item {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .order-item h3 {
            margin-top: 0;
            color: #333;
        }
        .item-row {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
            align-items: end;
        }
        .item-row > div {
            flex: 1;
        }
        .item-row button {
            background-color: #dc3545;
            padding: 8px 12px;
        }
        .total-section {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .status-paid { color: #28a745; }
        .status-partial { color: #ffc107; }
        .status-pending { color: #dc3545; }
    </style>
</head>
<body>
    <h1>🛒 销售订单管理系统</h1>
    
    <!-- 登录区域 -->
    <div class="container" id="loginSection">
        <h2>🔐 用户登录</h2>
        <div class="form-group">
            <label for="phone">手机号:</label>
            <input type="tel" id="phone" value="13800138000" placeholder="请输入手机号">
        </div>
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" value="admin123" placeholder="请输入密码">
        </div>
        <button onclick="login()">登录</button>
        <div id="loginMessage"></div>
    </div>

    <!-- 主要功能区域 -->
    <div class="container" id="mainSection" style="display: none;">
        <h2>创建销售订单</h2>
        
        <!-- 基本信息 -->
        <div class="form-group">
            <label for="customerId">客户:</label>
            <select id="customerId">
                <option value="">请选择客户</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="paymentMethod">付款方式:</label>
            <input type="text" id="paymentMethod" placeholder="如：微信、支付宝、现金等">
        </div>
        
        <div class="form-group">
            <label for="amountPaid">已付金额:</label>
            <input type="number" id="amountPaid" step="0.01" value="0" placeholder="已付金额">
        </div>
        
        <div class="form-group">
            <label for="orderRemark">订单备注:</label>
            <textarea id="orderRemark" rows="2" placeholder="订单备注信息"></textarea>
        </div>

        <!-- 订单明细 -->
        <h3>订单明细</h3>
        <div id="orderItems">
            <div class="item-row">
                <div>
                    <label>兔子类型:</label>
                    <select class="rabbit-type">
                        <option value="">请选择兔子类型</option>
                    </select>
                </div>
                <div>
                    <label>数量:</label>
                    <input type="number" class="quantity" step="0.01" placeholder="数量">
                </div>
                <div>
                    <label>单位:</label>
                    <input type="text" class="unit" placeholder="只/斤/对等">
                </div>
                <div>
                    <label>单价:</label>
                    <input type="number" class="unit-price" step="0.01" placeholder="单价">
                </div>
                <div>
                    <label>备注:</label>
                    <input type="text" class="item-remark" placeholder="明细备注">
                </div>
                <div>
                    <button onclick="removeItem(this)">删除</button>
                </div>
            </div>
        </div>
        
        <button onclick="addItem()">添加明细</button>
        <button onclick="calculateTotal()">计算总价</button>
        
        <!-- 总价显示 -->
        <div class="total-section">
            <h3>订单总计</h3>
            <p><strong>订单总价: </strong><span id="totalPrice">0.00</span> 元</p>
            <p><strong>已付金额: </strong><span id="paidAmount">0.00</span> 元</p>
            <p><strong>未付金额: </strong><span id="unpaidAmount">0.00</span> 元</p>
        </div>
        
        <button onclick="createOrder()">创建订单</button>
        <div id="orderMessage"></div>

        <!-- 订单列表 -->
        <h2>订单列表</h2>
        <button onclick="loadOrders()">刷新订单列表</button>
        <div id="ordersList">点击刷新加载订单列表...</div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/v1';
        let authToken = '';
        let customers = [];
        let rabbitTypes = [];
        let currentUserId = null;

        // 显示消息
        function showMessage(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${isError ? 'error' : 'success'}">${message}</div>`;
            setTimeout(() => {
                element.innerHTML = '';
            }, 3000);
        }

        // 获取认证头
        function getAuthHeaders() {
            return {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`
            };
        }

        // 用户登录
        async function login() {
            const phone = document.getElementById('phone').value;
            const password = document.getElementById('password').value;

            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ phone, password })
                });

                if (response.ok) {
                    const data = await response.json();
                    authToken = data.access_token;
                    
                    // 获取当前用户信息
                    const userResponse = await fetch(`${API_BASE}/users/me`, {
                        headers: getAuthHeaders()
                    });
                    if (userResponse.ok) {
                        const userInfo = await userResponse.json();
                        currentUserId = userInfo.id;
                    }
                    
                    document.getElementById('loginSection').style.display = 'none';
                    document.getElementById('mainSection').style.display = 'block';
                    showMessage('loginMessage', '登录成功！');
                    
                    // 加载基础数据
                    await loadCustomers();
                    await loadRabbitTypes();
                } else {
                    const error = await response.json();
                    showMessage('loginMessage', `登录失败: ${error.detail}`, true);
                }
            } catch (error) {
                showMessage('loginMessage', `登录失败: ${error.message}`, true);
            }
        }

        // 加载客户列表
        async function loadCustomers() {
            try {
                const response = await fetch(`${API_BASE}/customers/`, {
                    headers: getAuthHeaders()
                });

                if (response.ok) {
                    customers = await response.json();
                    const select = document.getElementById('customerId');
                    select.innerHTML = '<option value="">请选择客户</option>';
                    customers.forEach(customer => {
                        select.innerHTML += `<option value="${customer.id}">${customer.name} (${customer.phone})</option>`;
                    });
                }
            } catch (error) {
                console.error('加载客户失败:', error);
            }
        }

        // 加载兔子类型列表
        async function loadRabbitTypes() {
            try {
                const response = await fetch(`${API_BASE}/rabbit-types/all`, {
                    headers: getAuthHeaders()
                });

                if (response.ok) {
                    rabbitTypes = await response.json();
                    updateRabbitTypeSelects();
                }
            } catch (error) {
                console.error('加载兔子类型失败:', error);
            }
        }

        // 更新所有兔子类型下拉框
        function updateRabbitTypeSelects() {
            const selects = document.querySelectorAll('.rabbit-type');
            selects.forEach(select => {
                const currentValue = select.value;
                select.innerHTML = '<option value="">请选择兔子类型</option>';
                rabbitTypes.forEach(type => {
                    select.innerHTML += `<option value="${type.id}">${type.name}</option>`;
                });
                select.value = currentValue;
            });
        }

        // 添加订单明细行
        function addItem() {
            const container = document.getElementById('orderItems');
            const newRow = document.createElement('div');
            newRow.className = 'item-row';
            newRow.innerHTML = `
                <div>
                    <label>兔子类型:</label>
                    <select class="rabbit-type">
                        <option value="">请选择兔子类型</option>
                    </select>
                </div>
                <div>
                    <label>数量:</label>
                    <input type="number" class="quantity" step="0.01" placeholder="数量">
                </div>
                <div>
                    <label>单位:</label>
                    <input type="text" class="unit" placeholder="只/斤/对等">
                </div>
                <div>
                    <label>单价:</label>
                    <input type="number" class="unit-price" step="0.01" placeholder="单价">
                </div>
                <div>
                    <label>备注:</label>
                    <input type="text" class="item-remark" placeholder="明细备注">
                </div>
                <div>
                    <button onclick="removeItem(this)">删除</button>
                </div>
            `;
            container.appendChild(newRow);
            updateRabbitTypeSelects();
        }

        // 删除订单明细行
        function removeItem(button) {
            const rows = document.querySelectorAll('.item-row');
            if (rows.length > 1) {
                button.closest('.item-row').remove();
                calculateTotal();
            } else {
                alert('至少需要保留一行明细');
            }
        }

        // 计算总价
        function calculateTotal() {
            const rows = document.querySelectorAll('.item-row');
            let total = 0;
            
            rows.forEach(row => {
                const quantity = parseFloat(row.querySelector('.quantity').value) || 0;
                const unitPrice = parseFloat(row.querySelector('.unit-price').value) || 0;
                total += quantity * unitPrice;
            });
            
            const amountPaid = parseFloat(document.getElementById('amountPaid').value) || 0;
            const unpaid = Math.max(0, total - amountPaid);
            
            document.getElementById('totalPrice').textContent = total.toFixed(2);
            document.getElementById('paidAmount').textContent = amountPaid.toFixed(2);
            document.getElementById('unpaidAmount').textContent = unpaid.toFixed(2);
        }

        // 监听已付金额变化
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('amountPaid').addEventListener('input', calculateTotal);
        });

        // 创建订单
        async function createOrder() {
            const customerId = document.getElementById('customerId').value;
            const paymentMethod = document.getElementById('paymentMethod').value;
            const amountPaid = parseFloat(document.getElementById('amountPaid').value) || 0;
            const remark = document.getElementById('orderRemark').value;

            if (!customerId) {
                showMessage('orderMessage', '请选择客户', true);
                return;
            }

            // 收集订单明细
            const items = [];
            const rows = document.querySelectorAll('.item-row');
            
            for (let row of rows) {
                const rabbitTypeId = row.querySelector('.rabbit-type').value;
                const quantity = parseFloat(row.querySelector('.quantity').value);
                const unit = row.querySelector('.unit').value;
                const unitPrice = parseFloat(row.querySelector('.unit-price').value);
                const itemRemark = row.querySelector('.item-remark').value;

                if (!rabbitTypeId || !quantity || !unit || !unitPrice) {
                    showMessage('orderMessage', '请完整填写所有订单明细', true);
                    return;
                }

                items.push({
                    rabbit_type_id: parseInt(rabbitTypeId),
                    quantity: quantity,
                    unit: unit,
                    unit_price: unitPrice,
                    remark: itemRemark
                });
            }

            const orderData = {
                customer_id: parseInt(customerId),
                salesperson_id: currentUserId,
                payment_method: paymentMethod,
                amount_paid: amountPaid,
                remark: remark,
                items: items
            };

            try {
                const response = await fetch(`${API_BASE}/orders/`, {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify(orderData)
                });

                if (response.ok) {
                    const order = await response.json();
                    showMessage('orderMessage', `订单创建成功！订单号: ${order.id}`);
                    
                    // 清空表单
                    document.getElementById('customerId').value = '';
                    document.getElementById('paymentMethod').value = '';
                    document.getElementById('amountPaid').value = '0';
                    document.getElementById('orderRemark').value = '';
                    
                    // 重置明细
                    const container = document.getElementById('orderItems');
                    container.innerHTML = `
                        <div class="item-row">
                            <div>
                                <label>兔子类型:</label>
                                <select class="rabbit-type">
                                    <option value="">请选择兔子类型</option>
                                </select>
                            </div>
                            <div>
                                <label>数量:</label>
                                <input type="number" class="quantity" step="0.01" placeholder="数量">
                            </div>
                            <div>
                                <label>单位:</label>
                                <input type="text" class="unit" placeholder="只/斤/对等">
                            </div>
                            <div>
                                <label>单价:</label>
                                <input type="number" class="unit-price" step="0.01" placeholder="单价">
                            </div>
                            <div>
                                <label>备注:</label>
                                <input type="text" class="item-remark" placeholder="明细备注">
                            </div>
                            <div>
                                <button onclick="removeItem(this)">删除</button>
                            </div>
                        </div>
                    `;
                    updateRabbitTypeSelects();
                    calculateTotal();
                    loadOrders();
                } else {
                    const error = await response.json();
                    showMessage('orderMessage', `创建订单失败: ${error.detail}`, true);
                }
            } catch (error) {
                showMessage('orderMessage', `创建订单失败: ${error.message}`, true);
            }
        }

        // 加载订单列表
        async function loadOrders() {
            try {
                const response = await fetch(`${API_BASE}/orders/`, {
                    headers: getAuthHeaders()
                });

                if (response.ok) {
                    const orders = await response.json();
                    let html = '';
                    
                    if (orders.length === 0) {
                        html = '<p>暂无订单数据</p>';
                    } else {
                        orders.forEach(order => {
                            const statusClass = order.status === 'paid' ? 'status-paid' : 
                                              order.status === 'partial_paid' ? 'status-partial' : 'status-pending';
                            
                            html += `
                                <div class="order-item">
                                    <h3>订单 #${order.id}</h3>
                                    <p><strong>客户:</strong> ${order.customer_name}</p>
                                    <p><strong>销售员:</strong> ${order.salesperson_name}</p>
                                    <p><strong>总价:</strong> ${order.total_price} 元</p>
                                    <p><strong>已付:</strong> ${order.amount_paid} 元</p>
                                    <p><strong>未付:</strong> ${order.unpaid_amount} 元</p>
                                    <p><strong>状态:</strong> <span class="${statusClass}">${order.status_display}</span></p>
                                    <p><strong>创建时间:</strong> ${new Date(order.created_at).toLocaleString()}</p>
                                </div>
                            `;
                        });
                    }
                    
                    document.getElementById('ordersList').innerHTML = html;
                } else {
                    document.getElementById('ordersList').innerHTML = '<div class="error">加载订单失败</div>';
                }
            } catch (error) {
                document.getElementById('ordersList').innerHTML = '<div class="error">加载订单失败</div>';
            }
        }
    </script>
</body>
</html>
