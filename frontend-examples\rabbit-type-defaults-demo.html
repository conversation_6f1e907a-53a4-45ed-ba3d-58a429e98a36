<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>兔子类型默认值演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
            text-align: center;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .form-row {
            display: flex;
            gap: 15px;
        }
        .form-row .form-group {
            flex: 1;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        .btn-secondary {
            background-color: #2196F3;
        }
        .btn-secondary:hover {
            background-color: #1976D2;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .rabbit-types-list {
            margin-top: 20px;
        }
        .rabbit-type-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 10px;
        }
        .rabbit-type-header {
            font-weight: bold;
            font-size: 16px;
            color: #333;
            margin-bottom: 8px;
        }
        .rabbit-type-details {
            display: flex;
            gap: 20px;
            font-size: 14px;
            color: #666;
        }
        .default-value {
            background-color: #e7f3ff;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
        }
        .order-demo {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐇 兔子类型默认值演示</h1>
        
        <h2>创建兔子类型</h2>
        <form id="rabbitTypeForm">
            <div class="form-group">
                <label for="name">类型名称 *</label>
                <input type="text" id="name" required placeholder="例如：优质种兔">
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="defaultUnit">默认单位</label>
                    <select id="defaultUnit">
                        <option value="">不设置默认单位</option>
                        <option value="只">只</option>
                        <option value="斤">斤</option>
                        <option value="公斤">公斤</option>
                        <option value="kg">kg</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="defaultPrice">默认单价（元）</label>
                    <input type="number" id="defaultPrice" step="0.01" min="0" max="99999.99" 
                           placeholder="例如：25.00">
                </div>
            </div>
            
            <div class="form-group">
                <label for="remark">备注</label>
                <textarea id="remark" rows="3" placeholder="类型描述、用途说明等"></textarea>
            </div>
            
            <button type="button" onclick="createRabbitType()">创建兔子类型</button>
            <button type="button" class="btn-secondary" onclick="loadRabbitTypes()">刷新列表</button>
        </form>
        
        <div id="createResult" class="result"></div>
    </div>

    <div class="container">
        <h2>兔子类型列表</h2>
        <div id="rabbitTypesList" class="rabbit-types-list">
            <p>加载中...</p>
        </div>
    </div>

    <div class="container">
        <h2>订单录入演示</h2>
        <p>选择兔子类型，查看默认值如何自动填充到订单表单中：</p>
        
        <div class="form-group">
            <label for="orderRabbitType">选择兔子类型</label>
            <select id="orderRabbitType" onchange="fillOrderDefaults()">
                <option value="">请选择兔子类型</option>
            </select>
        </div>
        
        <div class="order-demo">
            <h3>订单明细（自动填充默认值）</h3>
            <div class="form-row">
                <div class="form-group">
                    <label for="orderQuantity">数量</label>
                    <input type="number" id="orderQuantity" step="0.1" value="1">
                </div>
                
                <div class="form-group">
                    <label for="orderUnit">单位</label>
                    <input type="text" id="orderUnit" placeholder="将自动填充默认单位">
                </div>
                
                <div class="form-group">
                    <label for="orderPrice">单价（元）</label>
                    <input type="number" id="orderPrice" step="0.01" placeholder="将自动填充默认单价">
                </div>
            </div>
            
            <div class="form-group">
                <label for="totalPrice">小计（元）</label>
                <input type="text" id="totalPrice" readonly style="background-color: #f8f9fa;">
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/v1';
        let authToken = '';
        let rabbitTypes = [];

        // 页面加载时初始化
        window.onload = async function() {
            await login();
            await loadRabbitTypes();
        };

        // 登录获取token
        async function login() {
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        phone: '13800138000',
                        password: 'admin123'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    authToken = data.access_token;
                } else {
                    showResult('createResult', '登录失败', 'error');
                }
            } catch (error) {
                showResult('createResult', `登录错误: ${error.message}`, 'error');
            }
        }

        // 创建兔子类型
        async function createRabbitType() {
            const name = document.getElementById('name').value.trim();
            const defaultUnit = document.getElementById('defaultUnit').value;
            const defaultPrice = document.getElementById('defaultPrice').value;
            const remark = document.getElementById('remark').value.trim();

            if (!name) {
                showResult('createResult', '请输入类型名称', 'error');
                return;
            }

            const data = { name, remark };
            if (defaultUnit) data.default_unit = defaultUnit;
            if (defaultPrice) data.default_price = parseFloat(defaultPrice);

            try {
                const response = await fetch(`${API_BASE}/rabbit-types/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(data)
                });

                if (response.ok) {
                    const rabbitType = await response.json();
                    showResult('createResult', `创建成功！ID: ${rabbitType.id}`, 'success');
                    
                    // 清空表单
                    document.getElementById('rabbitTypeForm').reset();
                    
                    // 刷新列表
                    await loadRabbitTypes();
                } else {
                    const error = await response.json();
                    showResult('createResult', `创建失败: ${error.detail}`, 'error');
                }
            } catch (error) {
                showResult('createResult', `创建错误: ${error.message}`, 'error');
            }
        }

        // 加载兔子类型列表
        async function loadRabbitTypes() {
            try {
                const response = await fetch(`${API_BASE}/rabbit-types/all`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                if (response.ok) {
                    rabbitTypes = await response.json();
                    displayRabbitTypes();
                    updateOrderSelect();
                } else {
                    document.getElementById('rabbitTypesList').innerHTML = '<p>加载失败</p>';
                }
            } catch (error) {
                document.getElementById('rabbitTypesList').innerHTML = '<p>加载错误</p>';
            }
        }

        // 显示兔子类型列表
        function displayRabbitTypes() {
            const container = document.getElementById('rabbitTypesList');
            
            if (rabbitTypes.length === 0) {
                container.innerHTML = '<p>暂无兔子类型</p>';
                return;
            }

            let html = '';
            rabbitTypes.forEach(type => {
                const unit = type.default_unit || '未设置';
                const price = type.default_price ? `${type.default_price}元` : '未设置';
                
                html += `
                    <div class="rabbit-type-item">
                        <div class="rabbit-type-header">${type.name} (ID: ${type.id})</div>
                        <div class="rabbit-type-details">
                            <span>默认单位: <span class="default-value">${unit}</span></span>
                            <span>默认单价: <span class="default-value">${price}</span></span>
                            <span>备注: ${type.remark || '无'}</span>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        // 更新订单选择下拉框
        function updateOrderSelect() {
            const select = document.getElementById('orderRabbitType');
            select.innerHTML = '<option value="">请选择兔子类型</option>';
            
            rabbitTypes.forEach(type => {
                const option = document.createElement('option');
                option.value = type.id;
                option.textContent = `${type.name} (${type.default_unit || '无单位'}, ${type.default_price || '无价格'}元)`;
                option.dataset.unit = type.default_unit || '';
                option.dataset.price = type.default_price || '';
                select.appendChild(option);
            });
        }

        // 填充订单默认值
        function fillOrderDefaults() {
            const select = document.getElementById('orderRabbitType');
            const selectedOption = select.options[select.selectedIndex];
            
            if (selectedOption.value) {
                const unit = selectedOption.dataset.unit;
                const price = selectedOption.dataset.price;
                
                document.getElementById('orderUnit').value = unit || '';
                document.getElementById('orderPrice').value = price || '';
                
                // 计算小计
                calculateTotal();
            } else {
                document.getElementById('orderUnit').value = '';
                document.getElementById('orderPrice').value = '';
                document.getElementById('totalPrice').value = '';
            }
        }

        // 计算小计
        function calculateTotal() {
            const quantity = parseFloat(document.getElementById('orderQuantity').value) || 0;
            const price = parseFloat(document.getElementById('orderPrice').value) || 0;
            const total = quantity * price;
            
            document.getElementById('totalPrice').value = total.toFixed(2);
        }

        // 监听数量和单价变化
        document.getElementById('orderQuantity').addEventListener('input', calculateTotal);
        document.getElementById('orderPrice').addEventListener('input', calculateTotal);

        // 显示结果
        function showResult(elementId, message, type) {
            const result = document.getElementById(elementId);
            result.textContent = message;
            result.className = `result ${type}`;
            result.style.display = 'block';
            
            setTimeout(() => {
                result.style.display = 'none';
            }, 3000);
        }
    </script>
</body>
</html>
