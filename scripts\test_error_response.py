#!/usr/bin/env python3
"""
测试错误响应的具体内容
"""

import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_error_response():
    print("🔍 测试错误响应内容...")
    
    # 1. 登录
    print("\n1. 登录...")
    login_data = {
        "phone": "13800138000",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    if response.status_code == 200:
        token_data = response.json()
        headers = {"Authorization": f"Bearer {token_data['access_token']}"}
        print("✅ 登录成功")
    else:
        print(f"❌ 登录失败: {response.text}")
        return
    
    # 2. 尝试删除有关联的兔子类型（ID=5）
    print("\n2. 尝试删除有关联的兔子类型（ID=5）...")
    response = requests.delete(f"{BASE_URL}/rabbit-types/5", headers=headers)
    
    print(f"状态码: {response.status_code}")
    print(f"响应头: {dict(response.headers)}")
    print(f"响应内容: {response.text}")
    
    if response.status_code == 400:
        try:
            error_data = response.json()
            print(f"解析后的错误数据: {json.dumps(error_data, ensure_ascii=False, indent=2)}")
            
            if 'detail' in error_data:
                print(f"错误详情: {error_data['detail']}")
            else:
                print("❌ 响应中没有detail字段")
                
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
    else:
        print(f"❌ 意外的状态码: {response.status_code}")
    
    # 3. 测试其他错误情况
    print("\n3. 测试404错误...")
    response = requests.delete(f"{BASE_URL}/rabbit-types/99999", headers=headers)
    print(f"404状态码: {response.status_code}")
    print(f"404响应内容: {response.text}")
    
    if response.status_code == 404:
        try:
            error_data = response.json()
            print(f"404错误详情: {error_data.get('detail', '无detail字段')}")
        except json.JSONDecodeError:
            print("❌ 404响应JSON解析失败")

if __name__ == "__main__":
    test_error_response()
