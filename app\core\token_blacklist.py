"""
Token黑名单管理模块
用于实现真正的退出登录功能
"""

import time
from datetime import datetime, timedelta
from typing import Set, Optional
from app.core.config import settings


class TokenBlacklist:
    """Token黑名单管理器"""
    
    def __init__(self):
        # 简单内存存储，生产环境建议使用Redis
        self._blacklisted_tokens: Set[str] = set()
        self._token_expiry: dict = {}  # 存储token的过期时间
    
    def add_token(self, token: str, expires_at: Optional[datetime] = None) -> None:
        """
        将token添加到黑名单
        
        Args:
            token: JWT token
            expires_at: token过期时间，如果不提供则使用默认过期时间
        """
        self._blacklisted_tokens.add(token)
        
        if expires_at is None:
            # 使用默认过期时间
            expires_at = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        
        self._token_expiry[token] = expires_at
    
    def is_blacklisted(self, token: str) -> bool:
        """
        检查token是否在黑名单中
        
        Args:
            token: JWT token
            
        Returns:
            bool: True if token is blacklisted, False otherwise
        """
        # 先清理过期的token
        self._cleanup_expired_tokens()
        
        return token in self._blacklisted_tokens
    
    def remove_token(self, token: str) -> bool:
        """
        从黑名单中移除token（一般不需要使用）
        
        Args:
            token: JWT token
            
        Returns:
            bool: True if token was removed, False if not found
        """
        if token in self._blacklisted_tokens:
            self._blacklisted_tokens.remove(token)
            self._token_expiry.pop(token, None)
            return True
        return False
    
    def _cleanup_expired_tokens(self) -> None:
        """清理已过期的token"""
        current_time = datetime.utcnow()
        expired_tokens = []
        
        for token, expiry_time in self._token_expiry.items():
            if current_time > expiry_time:
                expired_tokens.append(token)
        
        for token in expired_tokens:
            self._blacklisted_tokens.discard(token)
            self._token_expiry.pop(token, None)
    
    def get_blacklist_size(self) -> int:
        """获取黑名单大小"""
        self._cleanup_expired_tokens()
        return len(self._blacklisted_tokens)
    
    def clear_all(self) -> None:
        """清空所有黑名单（仅用于测试）"""
        self._blacklisted_tokens.clear()
        self._token_expiry.clear()


# 全局实例
token_blacklist = TokenBlacklist()
