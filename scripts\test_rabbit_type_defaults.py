#!/usr/bin/env python3
"""
测试兔子类型的默认单位和默认单价功能
"""

import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_rabbit_type_defaults():
    print("🐇 测试兔子类型默认单位和默认单价功能...")
    
    # 1. 登录
    print("\n1. 登录...")
    login_data = {"phone": "13800138000", "password": "admin123"}
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    
    if response.status_code == 200:
        token_data = response.json()
        headers = {"Authorization": f"Bearer {token_data['access_token']}"}
        print("✅ 登录成功")
    else:
        print(f"❌ 登录失败: {response.text}")
        return
    
    # 2. 创建带默认值的兔子类型
    print("\n2. 创建带默认值的兔子类型...")
    
    test_rabbit_types = [
        {
            "name": "优质种兔",
            "default_unit": "只",
            "default_price": 120.00,
            "remark": "高品质种兔，适合繁殖"
        },
        {
            "name": "精品肉兔",
            "default_unit": "斤",
            "default_price": 22.50,
            "remark": "优质肉兔，肉质鲜美"
        },
        {
            "name": "观赏兔",
            "default_unit": "只",
            "default_price": 88.00,
            "remark": "宠物观赏兔，品相优良"
        },
        {
            "name": "普通商品兔",
            "default_unit": "只",
            "default_price": 45.00,
            "remark": "普通商品兔，性价比高"
        }
    ]
    
    created_types = []
    for rabbit_type_data in test_rabbit_types:
        print(f"   创建: {rabbit_type_data['name']}...")
        response = requests.post(f"{BASE_URL}/rabbit-types/", json=rabbit_type_data, headers=headers)
        
        if response.status_code == 200:
            rabbit_type = response.json()
            created_types.append(rabbit_type)
            print(f"   ✅ 成功: ID {rabbit_type['id']}")
            print(f"      默认单位: {rabbit_type.get('default_unit', '未设置')}")
            print(f"      默认单价: {rabbit_type.get('default_price', '未设置')} 元")
        else:
            print(f"   ❌ 失败: {response.text}")
    
    # 3. 创建不带默认值的兔子类型
    print("\n3. 创建不带默认值的兔子类型...")
    simple_rabbit_type = {
        "name": "简单类型",
        "remark": "不设置默认单位和单价"
    }
    
    response = requests.post(f"{BASE_URL}/rabbit-types/", json=simple_rabbit_type, headers=headers)
    if response.status_code == 200:
        rabbit_type = response.json()
        print(f"✅ 创建成功: {rabbit_type['name']}")
        print(f"   默认单位: {rabbit_type.get('default_unit', 'null')}")
        print(f"   默认单价: {rabbit_type.get('default_price', 'null')}")
        created_types.append(rabbit_type)
    else:
        print(f"❌ 创建失败: {response.text}")
    
    # 4. 测试单位验证
    print("\n4. 测试单位验证...")
    invalid_unit_data = {
        "name": "无效单位测试",
        "default_unit": "个",  # 无效单位
        "default_price": 30.00
    }
    
    response = requests.post(f"{BASE_URL}/rabbit-types/", json=invalid_unit_data, headers=headers)
    if response.status_code == 422:
        error_data = response.json()
        print(f"✅ 单位验证正确工作: {error_data.get('detail', '验证失败')}")
    else:
        print(f"❌ 单位验证失效: {response.status_code}")
    
    # 5. 测试价格验证
    print("\n5. 测试价格验证...")
    invalid_price_data = {
        "name": "无效价格测试",
        "default_unit": "只",
        "default_price": -10.00  # 负数价格
    }
    
    response = requests.post(f"{BASE_URL}/rabbit-types/", json=invalid_price_data, headers=headers)
    if response.status_code == 422:
        error_data = response.json()
        print(f"✅ 价格验证正确工作: {error_data.get('detail', '验证失败')}")
    else:
        print(f"❌ 价格验证失效: {response.status_code}")
    
    # 6. 更新兔子类型的默认值
    print("\n6. 测试更新默认值...")
    if created_types:
        update_data = {
            "name": "更新后的种兔",
            "default_unit": "公斤",
            "default_price": 150.00,
            "remark": "更新后的描述"
        }
        
        rabbit_type_id = created_types[0]['id']
        response = requests.put(f"{BASE_URL}/rabbit-types/{rabbit_type_id}", json=update_data, headers=headers)
        
        if response.status_code == 200:
            updated_type = response.json()
            print(f"✅ 更新成功: {updated_type['name']}")
            print(f"   新默认单位: {updated_type.get('default_unit')}")
            print(f"   新默认单价: {updated_type.get('default_price')} 元")
        else:
            print(f"❌ 更新失败: {response.text}")
    
    # 7. 获取所有兔子类型，查看默认值
    print("\n7. 查看所有兔子类型的默认值...")
    response = requests.get(f"{BASE_URL}/rabbit-types/all", headers=headers)
    
    if response.status_code == 200:
        all_types = response.json()
        print("✅ 所有兔子类型:")
        
        for rabbit_type in all_types:
            unit = rabbit_type.get('default_unit', '未设置')
            price = rabbit_type.get('default_price', '未设置')
            print(f"   - {rabbit_type['name']}: {unit}, {price}元")
    else:
        print(f"❌ 获取兔子类型失败: {response.text}")
    
    # 8. 测试在订单中使用默认值
    print("\n8. 测试在订单中使用默认值...")
    if created_types:
        # 获取客户信息
        customers_response = requests.get(f"{BASE_URL}/customers/", headers=headers)
        if customers_response.status_code == 200:
            customers = customers_response.json()
            if customers:
                customer_id = customers[0]['id']
                user_response = requests.get(f"{BASE_URL}/users/me", headers=headers)
                user_info = user_response.json()
                salesperson_id = user_info['id']
                
                # 使用带默认值的兔子类型创建订单
                rabbit_type = created_types[0]
                order_data = {
                    "customer_id": customer_id,
                    "salesperson_id": salesperson_id,
                    "payment_method": "现金",
                    "amount_paid": 300,
                    "remark": "测试默认值订单",
                    "items": [
                        {
                            "rabbit_type_id": rabbit_type['id'],
                            "quantity": 2,
                            "unit": rabbit_type.get('default_unit', '只'),  # 使用默认单位
                            "unit_price": rabbit_type.get('default_price', 50),  # 使用默认单价
                            "remark": "使用默认值的订单明细"
                        }
                    ]
                }
                
                response = requests.post(f"{BASE_URL}/orders/", json=order_data, headers=headers)
                if response.status_code == 200:
                    order = response.json()
                    print(f"✅ 使用默认值创建订单成功: ID {order['id']}")
                    print(f"   订单明细使用了默认单位: {order['items'][0]['unit']}")
                    print(f"   订单明细使用了默认单价: {order['items'][0]['unit_price']} 元")
                else:
                    print(f"❌ 创建订单失败: {response.text}")
    
    print("\n🎉 兔子类型默认值功能测试完成！")
    print("\n📋 测试总结:")
    print("   ✅ 支持设置默认单位（只、斤、公斤、kg）")
    print("   ✅ 支持设置默认单价（非负数，最大99999.99）")
    print("   ✅ 字段验证正确工作")
    print("   ✅ 可以不设置默认值（可选字段）")
    print("   ✅ 支持更新默认值")
    print("   ✅ 可以在创建订单时使用默认值")
    print("\n💡 业务价值:")
    print("   - 提高订单录入效率")
    print("   - 减少手动输入错误")
    print("   - 标准化常用商品的单位和价格")
    print("   - 为前端提供默认值参考")

if __name__ == "__main__":
    test_rabbit_type_defaults()
