from typing import List, Optional
from pydantic import validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    PROJECT_NAME: str = "FastAPI Backend"
    VERSION: str = "1.0.0"
    DESCRIPTION: str = "A FastAPI backend application"
    API_V1_STR: str = "/api/v1"
    
    SECRET_KEY: str
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 480  # 8小时，适合工作日使用
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7      # 刷新token 7天有效期
    
    DATABASE_URL: str
    
    BACKEND_CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:8080",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:8080",
        "https://localhost:3000",
        "https://localhost:8080",
    ]

    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v):
        if isinstance(v, str):
            if v.startswith("[") and v.endswith("]"):
                # 处理JSON格式的字符串
                import json
                try:
                    return json.loads(v)
                except json.JSONDecodeError:
                    pass
            # 处理逗号分隔的字符串
            origins = [origin.strip().strip('"').strip("'") for origin in v.split(",")]
            return [origin for origin in origins if origin]
        elif isinstance(v, list):
            return v
        return []
    
    ENVIRONMENT: str = "development"
    
    # Email settings (optional)
    SMTP_TLS: bool = True
    SMTP_PORT: Optional[int] = None
    SMTP_HOST: Optional[str] = None
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    
    # Redis settings (optional)
    REDIS_URL: Optional[str] = None
    
    class Config:
        env_file = ".env"
        case_sensitive = True


settings = Settings()
