from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.api import deps
from app.crud.crud_user import user
from app.db.database import get_db
from app.db.models import User
from app.schemas.user import User as UserSchema, UserCreate, UserUpdate
from app.core.permissions import (
    require_permission, require_super_admin, Permission,
    can_access_user_data
)

router = APIRouter()


@router.get("/", response_model=List[UserSchema])
def read_users(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(require_permission(Permission.USER_READ)),
) -> Any:
    """
    Retrieve users.
    """
    users = user.get_multi(db, skip=skip, limit=limit)
    return users


@router.post("/", response_model=UserSchema)
def create_user(
    *,
    db: Session = Depends(get_db),
    user_in: UserCreate,
    current_user: User = Depends(require_permission(Permission.USER_CREATE)),
) -> Any:
    """
    创建新用户
    """
    existing_user = user.get_by_phone(db, phone=user_in.phone)
    if existing_user:
        raise HTTPException(
            status_code=400,
            detail="该手机号已被注册",
        )
    existing_user = user.get_by_username(db, username=user_in.username)
    if existing_user:
        raise HTTPException(
            status_code=400,
            detail="该用户名已被使用",
        )
    new_user = user.create(db, obj_in=user_in)
    return new_user


@router.put("/me", response_model=UserSchema)
def update_user_me(
    *,
    db: Session = Depends(get_db),
    user_update: UserUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    更新当前用户信息
    """
    # 检查手机号是否已被其他用户使用
    if user_update.phone and user_update.phone != current_user.phone:
        existing_user = user.get_by_phone(db, phone=user_update.phone)
        if existing_user:
            raise HTTPException(
                status_code=400,
                detail="该手机号已被其他用户使用",
            )

    # 检查用户名是否已被其他用户使用
    if user_update.username and user_update.username != current_user.username:
        existing_user = user.get_by_username(db, username=user_update.username)
        if existing_user:
            raise HTTPException(
                status_code=400,
                detail="该用户名已被其他用户使用",
            )

    updated_user = user.update(db, db_obj=current_user, obj_in=user_update)
    return updated_user


@router.get("/me")
def read_user_me(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取当前用户信息（优化返回字段）
    """
    from app.schemas.user import get_role_display_name

    # 构建优化的用户信息，移除不必要的字段
    from app.schemas.user import UserRole

    # 角色显示名称映射
    role_display_map = {
        "super_admin": "超级管理员",
        "admin": "普通管理员",
        "breeder": "饲养员",
        "salesperson": "销售员",
        "user": "普通用户"
    }

    # 获取角色值
    role_value = current_user.role.value if hasattr(current_user.role, 'value') else str(current_user.role)
    role_display = role_display_map.get(role_value, "普通用户")

    user_info = {
        "id": current_user.id,
        "phone": current_user.phone,
        "username": current_user.username,
        "role": role_value,
        "role_display": role_display,
        "is_active": current_user.is_active,
        "is_superuser": current_user.is_superuser,
    }

    return user_info


@router.get("/{user_id}", response_model=UserSchema)
def read_user_by_id(
    user_id: int,
    current_user: User = Depends(deps.get_current_active_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Get a specific user by id.
    """
    target_user = user.get(db, id=user_id)
    if target_user == current_user:
        return target_user
    if not user.is_superuser(current_user):
        raise HTTPException(
            status_code=400, detail="The user doesn't have enough privileges"
        )
    return target_user


@router.put("/{user_id}", response_model=UserSchema)
def update_user(
    *,
    db: Session = Depends(get_db),
    user_id: int,
    user_in: UserUpdate,
    current_user: User = Depends(deps.get_current_active_superuser),
) -> Any:
    """
    Update a user.
    """
    target_user = user.get(db, id=user_id)
    if not target_user:
        raise HTTPException(
            status_code=404,
            detail="The user with this id does not exist in the system",
        )
    updated_user = user.update(db, db_obj=target_user, obj_in=user_in)
    return updated_user
