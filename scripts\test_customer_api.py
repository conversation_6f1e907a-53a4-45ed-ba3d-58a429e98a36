#!/usr/bin/env python3
"""
测试客户和兔子类型API
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_login():
    """测试登录获取token"""
    login_data = {
        "phone": "13800138000",
        "password": "123456"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    if response.status_code == 200:
        token = response.json()["access_token"]
        print(f"✅ 登录成功，获取token: {token[:20]}...")
        return token
    else:
        print(f"❌ 登录失败: {response.text}")
        return None

def test_rabbit_types(token):
    """测试兔子类型API"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\n=== 测试兔子类型API ===")
    
    # 1. 创建兔子类型
    rabbit_type_data = {
        "name": "满月兔",
        "remark": "出生满一个月的小兔子"
    }
    
    response = requests.post(f"{BASE_URL}/rabbit-types/", json=rabbit_type_data, headers=headers)
    if response.status_code == 200:
        rabbit_type = response.json()
        print(f"✅ 创建兔子类型成功: {rabbit_type['name']}")
        rabbit_type_id = rabbit_type['id']
    else:
        print(f"❌ 创建兔子类型失败: {response.text}")
        return
    
    # 2. 获取兔子类型列表
    response = requests.get(f"{BASE_URL}/rabbit-types/", headers=headers)
    if response.status_code == 200:
        rabbit_types = response.json()
        print(f"✅ 获取兔子类型列表成功，共 {len(rabbit_types)} 个类型")
    else:
        print(f"❌ 获取兔子类型列表失败: {response.text}")
    
    # 3. 更新兔子类型
    update_data = {
        "remark": "出生满一个月的健康小兔子，适合新手饲养"
    }
    
    response = requests.put(f"{BASE_URL}/rabbit-types/{rabbit_type_id}", json=update_data, headers=headers)
    if response.status_code == 200:
        print("✅ 更新兔子类型成功")
    else:
        print(f"❌ 更新兔子类型失败: {response.text}")

def test_customers(token):
    """测试客户API"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\n=== 测试客户API ===")
    
    # 1. 创建客户
    customer_data = {
        "name": "张三",
        "phone": "13912345678",
        "address": "北京市朝阳区某某街道",
        "remark": "老客户，信誉良好"
    }
    
    response = requests.post(f"{BASE_URL}/customers/", json=customer_data, headers=headers)
    if response.status_code == 200:
        customer = response.json()
        print(f"✅ 创建客户成功: {customer['name']}")
        customer_id = customer['id']
    else:
        print(f"❌ 创建客户失败: {response.text}")
        return
    
    # 2. 获取客户列表
    response = requests.get(f"{BASE_URL}/customers/", headers=headers)
    if response.status_code == 200:
        customers = response.json()
        print(f"✅ 获取客户列表成功，共 {len(customers)} 个客户")
    else:
        print(f"❌ 获取客户列表失败: {response.text}")
    
    # 3. 搜索客户
    response = requests.get(f"{BASE_URL}/customers/?search_name=张", headers=headers)
    if response.status_code == 200:
        customers = response.json()
        print(f"✅ 搜索客户成功，找到 {len(customers)} 个匹配客户")
    else:
        print(f"❌ 搜索客户失败: {response.text}")
    
    # 4. 更新客户
    update_data = {
        "address": "北京市朝阳区新地址123号",
        "remark": "老客户，信誉良好，已搬家"
    }
    
    response = requests.put(f"{BASE_URL}/customers/{customer_id}", json=update_data, headers=headers)
    if response.status_code == 200:
        print("✅ 更新客户成功")
    else:
        print(f"❌ 更新客户失败: {response.text}")

def main():
    print("开始测试客户和兔子类型API...")
    
    # 1. 登录获取token
    token = test_login()
    if not token:
        print("无法获取token，测试终止")
        return
    
    # 2. 测试兔子类型API
    test_rabbit_types(token)
    
    # 3. 测试客户API
    test_customers(token)
    
    print("\n🎉 API测试完成！")

if __name__ == "__main__":
    main()
