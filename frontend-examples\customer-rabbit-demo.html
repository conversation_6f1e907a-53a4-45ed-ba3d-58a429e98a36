<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客户和兔子类型管理 - 演示页面 (CORS已完全开放)</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .success {
            color: #28a745;
            background-color: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .item {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .item h3 {
            margin-top: 0;
            color: #333;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            background-color: #e9ecef;
            border: 1px solid #ddd;
            cursor: pointer;
            border-radius: 4px 4px 0 0;
            margin-right: 5px;
        }
        .tab.active {
            background-color: #007bff;
            color: white;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <h1>🐇 客户和兔子类型管理系统</h1>
    
    <!-- 登录区域 -->
    <div class="container" id="loginSection">
        <h2>🔐 用户登录</h2>
        <div class="form-group">
            <label for="phone">手机号:</label>
            <input type="tel" id="phone" value="13800138000" placeholder="请输入手机号">
        </div>
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" value="admin123" placeholder="请输入密码">
        </div>
        <button onclick="login()">登录</button>
        <div id="loginMessage"></div>
    </div>

    <!-- 主要功能区域 -->
    <div class="container" id="mainSection" style="display: none;">
        <div class="tabs">
            <div class="tab active" onclick="showTab('rabbitTypes')">🐇 兔子类型管理</div>
            <div class="tab" onclick="showTab('customers')">👤 客户管理</div>
        </div>

        <!-- 兔子类型管理 -->
        <div id="rabbitTypes" class="tab-content active">
            <h2>兔子类型管理</h2>
            
            <!-- 创建兔子类型表单 -->
            <h3>创建新类型</h3>
            <div class="form-group">
                <label for="rabbitTypeName">类型名称:</label>
                <input type="text" id="rabbitTypeName" placeholder="如：满月兔、公种兔等">
            </div>
            <div class="form-group">
                <label for="rabbitTypeRemark">备注:</label>
                <textarea id="rabbitTypeRemark" rows="3" placeholder="用途、品种说明等"></textarea>
            </div>
            <button onclick="createRabbitType()">创建兔子类型</button>
            <div id="rabbitTypeMessage"></div>

            <!-- 兔子类型列表 -->
            <h3>兔子类型列表</h3>
            <button onclick="loadRabbitTypes()">刷新列表</button>
            <div id="rabbitTypesList" class="loading">点击刷新列表加载数据...</div>
        </div>

        <!-- 客户管理 -->
        <div id="customers" class="tab-content">
            <h2>客户管理</h2>
            
            <!-- 搜索客户 -->
            <div class="form-group">
                <label for="searchCustomer">搜索客户:</label>
                <input type="text" id="searchCustomer" placeholder="输入客户姓名或电话搜索" onkeyup="searchCustomers()">
            </div>

            <!-- 创建客户表单 -->
            <h3>创建新客户</h3>
            <div class="form-group">
                <label for="customerName">客户姓名:</label>
                <input type="text" id="customerName" placeholder="姓名或单位名称">
            </div>
            <div class="form-group">
                <label for="customerPhone">联系电话:</label>
                <input type="tel" id="customerPhone" placeholder="联系方式">
            </div>
            <div class="form-group">
                <label for="customerAddress">地址:</label>
                <input type="text" id="customerAddress" placeholder="客户地址">
            </div>
            <div class="form-group">
                <label for="customerRemark">备注:</label>
                <textarea id="customerRemark" rows="3" placeholder="客户备注信息"></textarea>
            </div>
            <button onclick="createCustomer()">创建客户</button>
            <div id="customerMessage"></div>

            <!-- 客户列表 -->
            <h3>客户列表</h3>
            <button onclick="loadCustomers()">刷新列表</button>
            <div id="customersList" class="loading">点击刷新列表加载数据...</div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/v1';
        let authToken = '';

        // 显示消息
        function showMessage(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${isError ? 'error' : 'success'}">${message}</div>`;
            setTimeout(() => {
                element.innerHTML = '';
            }, 3000);
        }

        // 获取认证头
        function getAuthHeaders() {
            return {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`
            };
        }

        // 用户登录
        async function login() {
            const phone = document.getElementById('phone').value;
            const password = document.getElementById('password').value;

            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ phone, password })
                });

                if (response.ok) {
                    const data = await response.json();
                    authToken = data.access_token;
                    document.getElementById('loginSection').style.display = 'none';
                    document.getElementById('mainSection').style.display = 'block';
                    showMessage('loginMessage', '登录成功！');
                } else {
                    const error = await response.json();
                    showMessage('loginMessage', `登录失败: ${error.detail}`, true);
                }
            } catch (error) {
                showMessage('loginMessage', `登录失败: ${error.message}`, true);
            }
        }

        // 切换标签页
        function showTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的标签
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        // 创建兔子类型
        async function createRabbitType() {
            const name = document.getElementById('rabbitTypeName').value;
            const remark = document.getElementById('rabbitTypeRemark').value;

            if (!name.trim()) {
                showMessage('rabbitTypeMessage', '请输入类型名称', true);
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/rabbit-types/`, {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify({ name, remark })
                });

                if (response.ok) {
                    showMessage('rabbitTypeMessage', '兔子类型创建成功！');
                    document.getElementById('rabbitTypeName').value = '';
                    document.getElementById('rabbitTypeRemark').value = '';
                    loadRabbitTypes(); // 刷新列表
                } else {
                    const error = await response.json();
                    showMessage('rabbitTypeMessage', `创建失败: ${error.detail}`, true);
                }
            } catch (error) {
                showMessage('rabbitTypeMessage', `创建失败: ${error.message}`, true);
            }
        }

        // 加载兔子类型列表
        async function loadRabbitTypes() {
            document.getElementById('rabbitTypesList').innerHTML = '<div class="loading">加载中...</div>';

            try {
                const response = await fetch(`${API_BASE}/rabbit-types/`, {
                    headers: getAuthHeaders()
                });

                if (response.ok) {
                    const rabbitTypes = await response.json();
                    let html = '';
                    
                    if (rabbitTypes.length === 0) {
                        html = '<p>暂无兔子类型数据</p>';
                    } else {
                        rabbitTypes.forEach(type => {
                            html += `
                                <div class="item">
                                    <h3>${type.name}</h3>
                                    <p><strong>备注:</strong> ${type.remark || '无'}</p>
                                </div>
                            `;
                        });
                    }
                    
                    document.getElementById('rabbitTypesList').innerHTML = html;
                } else {
                    document.getElementById('rabbitTypesList').innerHTML = '<div class="error">加载失败</div>';
                }
            } catch (error) {
                document.getElementById('rabbitTypesList').innerHTML = '<div class="error">加载失败</div>';
            }
        }

        // 创建客户
        async function createCustomer() {
            const name = document.getElementById('customerName').value;
            const phone = document.getElementById('customerPhone').value;
            const address = document.getElementById('customerAddress').value;
            const remark = document.getElementById('customerRemark').value;

            if (!name.trim() || !phone.trim()) {
                showMessage('customerMessage', '请输入客户姓名和联系电话', true);
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/customers/`, {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify({ name, phone, address, remark })
                });

                if (response.ok) {
                    showMessage('customerMessage', '客户创建成功！');
                    document.getElementById('customerName').value = '';
                    document.getElementById('customerPhone').value = '';
                    document.getElementById('customerAddress').value = '';
                    document.getElementById('customerRemark').value = '';
                    loadCustomers(); // 刷新列表
                } else {
                    const error = await response.json();
                    showMessage('customerMessage', `创建失败: ${error.detail}`, true);
                }
            } catch (error) {
                showMessage('customerMessage', `创建失败: ${error.message}`, true);
            }
        }

        // 加载客户列表
        async function loadCustomers(searchTerm = '') {
            document.getElementById('customersList').innerHTML = '<div class="loading">加载中...</div>';

            try {
                let url = `${API_BASE}/customers/`;
                if (searchTerm) {
                    url += `?search_name=${encodeURIComponent(searchTerm)}`;
                }

                const response = await fetch(url, {
                    headers: getAuthHeaders()
                });

                if (response.ok) {
                    const customers = await response.json();
                    let html = '';
                    
                    if (customers.length === 0) {
                        html = '<p>暂无客户数据</p>';
                    } else {
                        customers.forEach(customer => {
                            html += `
                                <div class="item">
                                    <h3>${customer.name}</h3>
                                    <p><strong>电话:</strong> ${customer.phone}</p>
                                    <p><strong>地址:</strong> ${customer.address || '无'}</p>
                                    <p><strong>备注:</strong> ${customer.remark || '无'}</p>
                                </div>
                            `;
                        });
                    }
                    
                    document.getElementById('customersList').innerHTML = html;
                } else {
                    document.getElementById('customersList').innerHTML = '<div class="error">加载失败</div>';
                }
            } catch (error) {
                document.getElementById('customersList').innerHTML = '<div class="error">加载失败</div>';
            }
        }

        // 搜索客户
        function searchCustomers() {
            const searchTerm = document.getElementById('searchCustomer').value;
            loadCustomers(searchTerm);
        }
    </script>
</body>
</html>
