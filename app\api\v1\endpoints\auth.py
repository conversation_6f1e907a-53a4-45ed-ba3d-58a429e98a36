from datetime import timed<PERSON><PERSON>
from typing import Any
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from app.api import deps
from app.core.config import settings
from app.core.security import create_access_token, create_refresh_token, verify_token
from app.core.captcha import captcha_manager, login_attempt_manager
from app.crud.crud_user import user
from app.db.database import get_db
from app.schemas.user import Token, LoginRequest, CaptchaResponse, LoginAttemptInfo, LogoutResponse

router = APIRouter()


@router.get("/captcha", response_model=CaptchaResponse)
def get_captcha() -> Any:
    """
    获取验证码
    """
    captcha_id, captcha_image = captcha_manager.create_captcha()
    return {
        "captcha_id": captcha_id,
        "captcha_image": captcha_image,
        "expires_in": 300
    }


@router.get("/login-attempts/{phone}", response_model=LoginAttemptInfo)
def get_login_attempts(phone: str) -> Any:
    """
    获取登录尝试信息
    """
    is_locked, locked_until = login_attempt_manager.is_locked(phone)
    remaining_attempts = login_attempt_manager.get_remaining_attempts(phone)

    return {
        "remaining_attempts": remaining_attempts,
        "is_locked": is_locked,
        "locked_until": locked_until,
        "requires_captcha": remaining_attempts <= 2  # 剩余2次或更少时需要验证码
    }


@router.post("/login", response_model=Token)
def login_access_token(
    login_data: LoginRequest,
    db: Session = Depends(get_db)
) -> Any:
    """
    手机号密码登录，获取访问令牌
    支持验证码防护
    """
    phone = login_data.phone

    # 检查是否被锁定
    is_locked, locked_until = login_attempt_manager.is_locked(phone)
    if is_locked:
        raise HTTPException(
            status_code=423,
            detail=f"账户已被锁定，请在 {locked_until.strftime('%Y-%m-%d %H:%M:%S')} 后重试"
        )

    # 检查是否需要验证码
    remaining_attempts = login_attempt_manager.get_remaining_attempts(phone)
    requires_captcha = remaining_attempts <= 2

    if requires_captcha:
        if not login_data.captcha_id or not login_data.captcha_code:
            raise HTTPException(
                status_code=400,
                detail="登录失败次数过多，请提供验证码"
            )

        # 验证验证码（必须在验证用户凭据之前）
        if not captcha_manager.verify_captcha(login_data.captcha_id, login_data.captcha_code):
            login_attempt_manager.record_attempt(phone, success=False)
            remaining = login_attempt_manager.get_remaining_attempts(phone)
            raise HTTPException(
                status_code=400,
                detail=f"验证码错误，剩余尝试次数：{remaining}"
            )

    # 验证用户凭据
    user_obj = user.authenticate(
        db, phone=phone, password=login_data.password
    )

    if not user_obj:
        login_attempt_manager.record_attempt(phone, success=False)
        remaining = login_attempt_manager.get_remaining_attempts(phone)
        if remaining > 0:
            detail = f"手机号或密码错误，剩余尝试次数：{remaining}"
            if remaining <= 2:
                detail += "，下次登录需要验证码"
        else:
            detail = "手机号或密码错误，账户已被锁定"
        raise HTTPException(status_code=400, detail=detail)

    if not user.is_active(user_obj):
        raise HTTPException(status_code=400, detail="用户已被禁用")

    # 登录成功，清除失败记录
    login_attempt_manager.record_attempt(phone, success=True)

    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    refresh_token_expires = timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)

    return {
        "access_token": create_access_token(
            data={"sub": str(user_obj.id)}, expires_delta=access_token_expires
        ),
        "refresh_token": create_refresh_token(
            data={"sub": str(user_obj.id)}, expires_delta=refresh_token_expires
        ),
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,  # 转换为秒
    }


@router.post("/login-oauth2", response_model=Token)
def login_oauth2_compatible(
    db: Session = Depends(get_db), form_data: OAuth2PasswordRequestForm = Depends()
) -> Any:
    """
    OAuth2兼容的登录接口（用于Swagger UI测试）
    username字段请输入手机号
    """
    user_obj = user.authenticate(
        db, phone=form_data.username, password=form_data.password
    )
    if not user_obj:
        raise HTTPException(status_code=400, detail="手机号或密码错误")
    elif not user.is_active(user_obj):
        raise HTTPException(status_code=400, detail="用户已被禁用")
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    return {
        "access_token": create_access_token(
            data={"sub": str(user_obj.id)}, expires_delta=access_token_expires
        ),
        "token_type": "bearer",
    }


@router.post("/refresh", response_model=Token)
def refresh_token(
    request: dict,
    db: Session = Depends(get_db)
) -> Any:
    """
    使用刷新token获取新的访问token
    """
    # 获取刷新token
    refresh_token_str = request.get("refresh_token")
    if not refresh_token_str:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="缺少refresh_token参数",
        )

    # 验证刷新token
    payload = verify_token(refresh_token_str, token_type="refresh")
    if not payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的刷新token",
        )

    user_id = payload.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的token数据",
        )

    # 验证用户是否存在且活跃
    user_obj = user.get(db, id=user_id)
    if not user_obj or not user.is_active(user_obj):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户不存在或已被禁用",
        )

    # 生成新的访问token和刷新token
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    refresh_token_expires = timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)

    return {
        "access_token": create_access_token(
            data={"sub": str(user_obj.id)}, expires_delta=access_token_expires
        ),
        "refresh_token": create_refresh_token(
            data={"sub": str(user_obj.id)}, expires_delta=refresh_token_expires
        ),
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
    }


@router.post("/logout", response_model=LogoutResponse)
def logout(
    current_user = Depends(deps.get_current_user),
    token: str = Depends(deps.get_current_token)
) -> Any:
    """
    用户退出登录
    将当前token加入黑名单，实现真正的退出登录
    """
    from app.core.token_blacklist import token_blacklist
    from app.core.security import get_token_expiry
    from datetime import datetime

    # 获取token过期时间
    expires_at = get_token_expiry(token)

    # 将token添加到黑名单
    token_blacklist.add_token(token, expires_at)

    return {
        "message": "退出登录成功",
        "logged_out_at": datetime.utcnow()
    }


@router.post("/test-token")
def test_token(current_user = Depends(deps.get_current_user)) -> Any:
    """
    Test access token
    """
    return current_user
