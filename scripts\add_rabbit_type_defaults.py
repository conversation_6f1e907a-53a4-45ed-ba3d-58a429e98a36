#!/usr/bin/env python3
"""
为兔子类型表添加默认单位和默认单价字段
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from app.db.database import engine

def add_rabbit_type_default_fields():
    print("🔧 为兔子类型表添加默认单位和默认单价字段...")
    
    try:
        with engine.connect() as connection:
            # 开始事务
            trans = connection.begin()
            
            try:
                # 检查字段是否已存在
                print("1. 检查字段是否已存在...")
                result = connection.execute(text("""
                    SELECT COLUMN_NAME 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_NAME = 'rabbit_types' 
                    AND COLUMN_NAME IN ('default_unit', 'default_price')
                """))
                
                existing_columns = [row[0] for row in result.fetchall()]
                print(f"   已存在的字段: {existing_columns}")
                
                # 添加 default_unit 字段
                if 'default_unit' not in existing_columns:
                    print("2. 添加 default_unit 字段...")
                    connection.execute(text("""
                        ALTER TABLE rabbit_types 
                        ADD COLUMN default_unit VARCHAR(10) NULL 
                        COMMENT '默认单位：只、斤、公斤等'
                    """))
                    print("   ✅ default_unit 字段添加成功")
                else:
                    print("2. default_unit 字段已存在，跳过")
                
                # 添加 default_price 字段
                if 'default_price' not in existing_columns:
                    print("3. 添加 default_price 字段...")
                    connection.execute(text("""
                        ALTER TABLE rabbit_types 
                        ADD COLUMN default_price DECIMAL(10,2) NULL 
                        COMMENT '默认单价（元）'
                    """))
                    print("   ✅ default_price 字段添加成功")
                else:
                    print("3. default_price 字段已存在，跳过")
                
                # 提交事务
                trans.commit()
                print("✅ 数据库字段添加完成！")
                
                # 验证字段添加结果
                print("\n4. 验证字段添加结果...")
                result = connection.execute(text("""
                    DESCRIBE rabbit_types
                """))
                
                print("   rabbit_types 表结构:")
                for row in result.fetchall():
                    field_name = row[0]
                    field_type = row[1]
                    is_null = row[2]
                    default_val = row[4]
                    comment = row[5] if len(row) > 5 else ''
                    
                    if field_name in ['default_unit', 'default_price']:
                        print(f"   ✅ {field_name}: {field_type} (NULL: {is_null}) - {comment}")
                
            except Exception as e:
                trans.rollback()
                raise e
                
    except Exception as e:
        print(f"❌ 添加字段失败: {e}")
        return False
    
    return True

def update_existing_rabbit_types():
    """为现有的兔子类型添加一些默认值"""
    print("\n🔄 为现有兔子类型添加默认值...")
    
    # 预设的默认值
    default_values = {
        "满月兔": {"unit": "只", "price": 25.00},
        "种兔": {"unit": "只", "price": 80.00},
        "肉兔": {"unit": "斤", "price": 18.00},
        "商品兔": {"unit": "只", "price": 35.00},
        "淘汰兔": {"unit": "斤", "price": 15.00},
    }
    
    try:
        with engine.connect() as connection:
            trans = connection.begin()
            
            try:
                # 获取现有的兔子类型
                result = connection.execute(text("SELECT id, name FROM rabbit_types"))
                existing_types = result.fetchall()
                
                print(f"   找到 {len(existing_types)} 个现有类型")
                
                updated_count = 0
                for type_id, type_name in existing_types:
                    if type_name in default_values:
                        defaults = default_values[type_name]
                        
                        connection.execute(text("""
                            UPDATE rabbit_types 
                            SET default_unit = :unit, default_price = :price 
                            WHERE id = :id
                        """), {
                            "unit": defaults["unit"],
                            "price": defaults["price"],
                            "id": type_id
                        })
                        
                        print(f"   ✅ 更新 {type_name}: {defaults['unit']}, {defaults['price']}元")
                        updated_count += 1
                
                trans.commit()
                print(f"✅ 成功更新 {updated_count} 个兔子类型的默认值")
                
            except Exception as e:
                trans.rollback()
                raise e
                
    except Exception as e:
        print(f"❌ 更新默认值失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🚀 开始兔子类型表结构升级...")
    
    # 1. 添加字段
    if add_rabbit_type_default_fields():
        # 2. 更新现有数据的默认值
        update_existing_rabbit_types()
        
        print("\n🎉 兔子类型表升级完成！")
        print("\n📋 新增功能:")
        print("   ✅ default_unit: 默认单位字段（只、斤、公斤、kg）")
        print("   ✅ default_price: 默认单价字段（元）")
        print("   ✅ 字段验证: 单位限制、价格范围验证")
        print("   ✅ 向后兼容: 现有数据不受影响")
        print("\n💡 使用场景:")
        print("   - 创建订单时自动填充默认单位和单价")
        print("   - 提高数据录入效率")
        print("   - 减少录入错误")
    else:
        print("❌ 表结构升级失败")
        sys.exit(1)
