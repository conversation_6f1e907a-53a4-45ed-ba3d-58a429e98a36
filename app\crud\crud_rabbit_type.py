"""
兔子类型CRUD操作
"""

from typing import List, Optional
from sqlalchemy.orm import Session
from app.crud.base import CRUDBase
from app.db.models import RabbitType
from app.schemas.rabbit_type import RabbitTypeCreate, RabbitTypeUpdate


class CRUDRabbitType(CRUDBase[RabbitType, RabbitTypeCreate, RabbitTypeUpdate]):
    """兔子类型CRUD操作类"""

    def get_by_name(self, db: Session, *, name: str) -> Optional[RabbitType]:
        """根据类型名称获取兔子类型"""
        return db.query(RabbitType).filter(RabbitType.name == name).first()

    def search_by_name(self, db: Session, *, name: str, skip: int = 0, limit: int = 100) -> List[RabbitType]:
        """根据类型名称模糊搜索兔子类型"""
        return (
            db.query(RabbitType)
            .filter(RabbitType.name.contains(name))
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_multi_ordered(
        self, db: Session, *, skip: int = 0, limit: int = 100, order_by: str = "created_at"
    ) -> List[RabbitType]:
        """获取多个兔子类型，支持排序"""
        query = db.query(RabbitType)
        
        if order_by == "name":
            query = query.order_by(RabbitType.name)
        elif order_by == "updated_at":
            query = query.order_by(RabbitType.updated_at.desc())
        else:  # 默认按创建时间倒序
            query = query.order_by(RabbitType.created_at.desc())
            
        return query.offset(skip).limit(limit).all()

    def get_all_active(self, db: Session) -> List[RabbitType]:
        """获取所有兔子类型（用于下拉选择等）"""
        return db.query(RabbitType).order_by(RabbitType.name).all()


# 创建实例
rabbit_type = CRUDRabbitType(RabbitType)
