#!/usr/bin/env python3
"""
快速测试错误响应
"""

import requests

BASE_URL = "http://localhost:8000/api/v1"

# 1. 登录获取token
login_data = {"phone": "13800138000", "password": "admin123"}
response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
token = response.json()['access_token']
headers = {"Authorization": f"Bearer {token}"}

# 2. 测试删除有关联的兔子类型
print("测试删除兔子类型ID=5...")
response = requests.delete(f"{BASE_URL}/rabbit-types/5", headers=headers)

print(f"状态码: {response.status_code}")
print(f"Content-Type: {response.headers.get('content-type')}")
print(f"响应内容: {response.text}")

if response.status_code == 400:
    try:
        error_data = response.json()
        print(f"错误详情: {error_data.get('detail', '无detail字段')}")
    except:
        print("JSON解析失败")
