from datetime import datetime, timedelta
from typing import Optional
from jose import JW<PERSON>rror, jwt
from passlib.context import Crypt<PERSON>ontext
from app.core.config import settings
import hashlib

# 尝试使用bcrypt，如果失败则使用pbkdf2_sha256
try:
    pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
    # 测试bcrypt是否可用
    pwd_context.hash("test")
except Exception:
    # 如果bcrypt不可用，使用pbkdf2_sha256
    pwd_context = CryptContext(schemes=["pbkdf2_sha256"], deprecated="auto")


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire, "type": "access"})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt


def create_refresh_token(data: dict, expires_delta: Optional[timedelta] = None):
    """创建刷新token"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
    to_encode.update({"exp": expire, "type": "refresh"})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt


def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)


def verify_token(token: str, token_type: str = "access") -> Optional[dict]:
    """验证JWT token并检查黑名单"""
    from app.core.token_blacklist import token_blacklist

    # 首先检查token是否在黑名单中
    if token_blacklist.is_blacklisted(token):
        return None

    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])

        # 检查token类型
        if payload.get("type") != token_type:
            return None

        # 将sub转换回整数
        if "sub" in payload:
            payload["sub"] = int(payload["sub"])
        return payload
    except (JWTError, ValueError):
        return None


def get_token_expiry(token: str) -> Optional[datetime]:
    """获取token的过期时间"""
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        exp_timestamp = payload.get("exp")
        if exp_timestamp:
            return datetime.utcfromtimestamp(exp_timestamp)
        return None
    except (JWTError, ValueError):
        return None
