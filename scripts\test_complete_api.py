#!/usr/bin/env python3
"""
测试完整的API功能 - 验证API_DOCUMENTATION.md中的所有示例
"""

import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_complete_api():
    print("🧪 测试完整API功能（基于API_DOCUMENTATION.md）...")
    
    # 1. 登录
    print("\n1. 🔐 测试登录...")
    login_data = {
        "phone": "13800138000",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    if response.status_code == 200:
        token_data = response.json()
        print(f"✅ 登录成功")
        print(f"   - Access Token有效期: {token_data['expires_in']} 秒 ({token_data['expires_in']//3600} 小时)")
        access_token = token_data['access_token']
        headers = {"Authorization": f"Bearer {access_token}"}
    else:
        print(f"❌ 登录失败: {response.text}")
        return
    
    # 2. 用户管理
    print("\n2. 👥 测试用户管理...")
    response = requests.get(f"{BASE_URL}/users/me", headers=headers)
    if response.status_code == 200:
        user_info = response.json()
        print(f"✅ 获取当前用户成功: {user_info['username']} ({user_info['role']})")
        salesperson_id = user_info['id']
    else:
        print(f"❌ 获取用户信息失败: {response.text}")
        return
    
    # 3. 客户管理
    print("\n3. 👤 测试客户管理...")
    
    # 创建客户
    customer_data = {
        "name": "API测试客户",
        "phone": f"139{hash('test') % 10000000:07d}",
        "address": "测试地址",
        "remark": "API文档测试客户"
    }
    
    response = requests.post(f"{BASE_URL}/customers/", json=customer_data, headers=headers)
    if response.status_code == 200:
        customer = response.json()
        print(f"✅ 创建客户成功: {customer['name']}")
        customer_id = customer['id']
    else:
        print(f"❌ 创建客户失败: {response.text}")
        return
    
    # 获取客户列表
    response = requests.get(f"{BASE_URL}/customers/", headers=headers)
    if response.status_code == 200:
        customers = response.json()
        print(f"✅ 获取客户列表成功，共 {len(customers)} 个客户")
    else:
        print(f"❌ 获取客户列表失败: {response.text}")
    
    # 4. 兔子类型管理
    print("\n4. 🐇 测试兔子类型管理...")
    
    # 创建兔子类型
    rabbit_type_data = {
        "name": f"API测试类型_{hash('rabbit') % 1000}",
        "remark": "API文档测试类型"
    }
    
    response = requests.post(f"{BASE_URL}/rabbit-types/", json=rabbit_type_data, headers=headers)
    if response.status_code == 200:
        rabbit_type = response.json()
        print(f"✅ 创建兔子类型成功: {rabbit_type['name']}")
        rabbit_type_id = rabbit_type['id']
    else:
        print(f"❌ 创建兔子类型失败: {response.text}")
        return
    
    # 获取所有兔子类型
    response = requests.get(f"{BASE_URL}/rabbit-types/all", headers=headers)
    if response.status_code == 200:
        rabbit_types = response.json()
        print(f"✅ 获取所有兔子类型成功，共 {len(rabbit_types)} 个类型")
    else:
        print(f"❌ 获取兔子类型失败: {response.text}")
    
    # 5. 销售订单管理
    print("\n5. 🛒 测试销售订单管理...")
    
    # 创建销售订单（按照文档示例）
    order_data = {
        "customer_id": customer_id,
        "salesperson_id": salesperson_id,
        "payment_method": "微信",
        "amount_paid": 500,
        "remark": "API文档测试订单",
        "items": [
            {
                "rabbit_type_id": rabbit_type_id,
                "quantity": 10,
                "unit": "只",
                "unit_price": 30,
                "remark": "测试种兔"
            },
            {
                "rabbit_type_id": rabbit_type_id,
                "quantity": 20.5,
                "unit": "斤",
                "unit_price": 18,
                "remark": "测试淘汰兔"
            }
        ]
    }
    
    response = requests.post(f"{BASE_URL}/orders/", json=order_data, headers=headers)
    if response.status_code == 200:
        order = response.json()
        print(f"✅ 创建订单成功: ID {order['id']}")
        print(f"   - 总价: {order['total_price']} 元")
        print(f"   - 已付: {order['amount_paid']} 元")
        print(f"   - 未付: {order['unpaid_amount']} 元")
        print(f"   - 状态: {order['status_display']}")
        order_id = order['id']
    else:
        print(f"❌ 创建订单失败: {response.text}")
        return
    
    # 添加付款
    payment_params = {
        "additional_payment": 169,
        "payment_method": "支付宝"
    }
    response = requests.post(f"{BASE_URL}/orders/{order_id}/payment", params=payment_params, headers=headers)
    if response.status_code == 200:
        updated_order = response.json()
        print(f"✅ 添加付款成功")
        print(f"   - 更新后状态: {updated_order['status_display']}")
        print(f"   - 未付金额: {updated_order['unpaid_amount']} 元")
    else:
        print(f"❌ 添加付款失败: {response.text}")
    
    # 获取订单列表
    response = requests.get(f"{BASE_URL}/orders/", headers=headers)
    if response.status_code == 200:
        orders = response.json()
        print(f"✅ 获取订单列表成功，共 {len(orders)} 个订单")
    else:
        print(f"❌ 获取订单列表失败: {response.text}")
    
    # 6. Token刷新测试
    print("\n6. 🔄 测试Token刷新...")
    refresh_data = {"refresh_token": token_data['refresh_token']}
    response = requests.post(f"{BASE_URL}/auth/refresh", json=refresh_data)
    if response.status_code == 200:
        new_token_data = response.json()
        print(f"✅ Token刷新成功")
        print(f"   - 新Token有效期: {new_token_data['expires_in']} 秒")
    else:
        print(f"❌ Token刷新失败: {response.text}")
    
    # 7. 退出登录
    print("\n7. 🚪 测试退出登录...")
    response = requests.post(f"{BASE_URL}/auth/logout", headers=headers)
    if response.status_code == 200:
        logout_info = response.json()
        print(f"✅ 退出登录成功: {logout_info['message']}")
    else:
        print(f"❌ 退出登录失败: {response.text}")
    
    print("\n🎉 完整API测试完成！")
    print("\n📚 所有功能都按照 API_DOCUMENTATION.md 文档正常工作")

if __name__ == "__main__":
    test_complete_api()
