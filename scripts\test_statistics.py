#!/usr/bin/env python3
"""
测试统计API
"""

import requests
import json
from datetime import datetime, timedelta

BASE_URL = "http://localhost:8000/api/v1"

def test_statistics():
    print("📊 测试统计API...")
    
    # 1. 登录
    print("\n1. 登录...")
    login_data = {"phone": "13800138000", "password": "admin123"}
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    
    if response.status_code == 200:
        token_data = response.json()
        headers = {"Authorization": f"Bearer {token_data['access_token']}"}
        print("✅ 登录成功")
    else:
        print(f"❌ 登录失败: {response.text}")
        return
    
    # 2. 测试订单统计摘要
    print("\n2. 测试订单统计摘要...")
    response = requests.get(f"{BASE_URL}/statistics/orders/summary", headers=headers)
    
    if response.status_code == 200:
        summary = response.json()
        print("✅ 订单统计摘要:")
        print(f"   总订单数: {summary['summary']['total_orders']}")
        print(f"   总金额: {summary['summary']['total_amount']:.2f} 元")
        print(f"   已付金额: {summary['summary']['paid_amount']:.2f} 元")
        print(f"   未付金额: {summary['summary']['unpaid_amount']:.2f} 元")
        
        print("   按状态统计:")
        for status, data in summary['status_breakdown'].items():
            print(f"     {status}: {data['count']} 个订单, {data['total_amount']:.2f} 元")
        
        if summary['salesperson_breakdown']:
            print("   按销售员统计:")
            for sp in summary['salesperson_breakdown']:
                print(f"     {sp['salesperson_name']}: {sp['order_count']} 个订单, {sp['total_amount']:.2f} 元")
    else:
        print(f"❌ 获取订单统计失败: {response.text}")
    
    # 3. 测试订单趋势
    print("\n3. 测试订单趋势（最近7天）...")
    response = requests.get(f"{BASE_URL}/statistics/orders/trends?days=7", headers=headers)
    
    if response.status_code == 200:
        trends = response.json()
        print("✅ 订单趋势:")
        print(f"   统计期间: {trends['period']['start_date']} 到 {trends['period']['end_date']}")
        
        for trend in trends['trends'][-3:]:  # 显示最近3天
            print(f"   {trend['date']}: {trend['order_count']} 个订单, {trend['total_amount']:.2f} 元")
    else:
        print(f"❌ 获取订单趋势失败: {response.text}")
    
    # 4. 测试产品销售排行
    print("\n4. 测试产品销售排行...")
    response = requests.get(f"{BASE_URL}/statistics/products/ranking?limit=5", headers=headers)
    
    if response.status_code == 200:
        ranking = response.json()
        print("✅ 产品销售排行（前5名）:")
        
        for product in ranking['ranking']:
            print(f"   第{product['rank']}名: {product['product_name']}")
            print(f"     销售数量: {product['total_quantity']}")
            print(f"     销售金额: {product['total_amount']:.2f} 元")
            print(f"     订单数量: {product['order_count']}")
    else:
        print(f"❌ 获取产品排行失败: {response.text}")
    
    # 5. 测试客户消费排行
    print("\n5. 测试客户消费排行...")
    response = requests.get(f"{BASE_URL}/statistics/customers/ranking?limit=5", headers=headers)
    
    if response.status_code == 200:
        ranking = response.json()
        print("✅ 客户消费排行（前5名）:")
        
        for customer in ranking['ranking']:
            print(f"   第{customer['rank']}名: {customer['customer_name']} ({customer['customer_phone']})")
            print(f"     订单数量: {customer['order_count']}")
            print(f"     消费金额: {customer['total_amount']:.2f} 元")
            print(f"     已付金额: {customer['paid_amount']:.2f} 元")
    else:
        print(f"❌ 获取客户排行失败: {response.text}")
    
    # 6. 测试带日期范围的统计
    print("\n6. 测试带日期范围的统计...")
    today = datetime.now().date()
    week_ago = today - timedelta(days=7)
    
    params = {
        "start_date": week_ago.strftime("%Y-%m-%d"),
        "end_date": today.strftime("%Y-%m-%d")
    }
    
    response = requests.get(f"{BASE_URL}/statistics/orders/summary", params=params, headers=headers)
    
    if response.status_code == 200:
        summary = response.json()
        print(f"✅ 最近7天统计 ({week_ago} 到 {today}):")
        print(f"   订单数: {summary['summary']['total_orders']}")
        print(f"   总金额: {summary['summary']['total_amount']:.2f} 元")
    else:
        print(f"❌ 获取日期范围统计失败: {response.text}")
    
    print("\n🎉 统计API测试完成！")
    print("\n📋 统计API优势:")
    print("   ✅ 后端聚合计算，性能优秀")
    print("   ✅ 只传输统计结果，网络开销小")
    print("   ✅ 支持权限控制，数据安全")
    print("   ✅ 支持日期范围和条件过滤")
    print("   ✅ 多维度统计分析")

if __name__ == "__main__":
    test_statistics()
