version: '3.8'

services:
  web:
    build: .
    ports:
      - "8000:8000"
    environment:
      # 云数据库配置
      - DATABASE_URL=mysql+pymysql://lptcgl:<EMAIL>:3306/lptcgl
      - SECRET_KEY=lptcgl-super-secret-key-2024
      - ENVIRONMENT=production
    volumes:
      - .:/app
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload
    # 注释掉本地数据库依赖，因为使用云数据库
    # depends_on:
    #   - db

  # 本地MySQL数据库服务（使用云数据库时可以注释掉）
  # db:
  #   image: mysql:8.0
  #   environment:
  #     - MYSQL_ROOT_PASSWORD=password
  #     - MYSQL_DATABASE=fastapi_db
  #     - MYSQL_USER=fastapi
  #     - MYSQL_PASSWORD=password
  #   ports:
  #     - "3306:3306"
  #   volumes:
  #     - mysql_data:/var/lib/mysql
  #   command: --default-authentication-plugin=mysql_native_password

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

volumes:
  mysql_data:
