<template>
  <div class="login-container">
    <h2>🚀 FastAPI 登录</h2>
    
    <!-- 消息显示 -->
    <div v-if="message.text" :class="['message', message.type]">
      {{ message.text }}
    </div>

    <!-- 登录状态信息 -->
    <div v-if="formData.phone.length === 11" class="status-info">
      剩余尝试: {{ loginStatus.remainingAttempts }} | 
      需要验证码: {{ loginStatus.requiresCaptcha ? '是' : '否' }} | 
      状态: {{ loginStatus.isLocked ? '已锁定' : '正常' }}
    </div>

    <!-- 登录表单 -->
    <form v-if="!user" @submit.prevent="handleLogin">
      <!-- 手机号输入 -->
      <div class="form-group">
        <label>手机号:</label>
        <input
          v-model="formData.phone"
          type="tel"
          placeholder="请输入11位手机号"
          maxlength="11"
          required
          @blur="checkLoginStatus"
        />
      </div>

      <!-- 密码输入 -->
      <div class="form-group">
        <label>密码:</label>
        <input
          v-model="formData.password"
          type="password"
          placeholder="请输入密码"
          required
        />
      </div>

      <!-- 验证码 -->
      <div v-if="loginStatus.requiresCaptcha" class="captcha-container">
        <label>验证码:</label>
        <div class="captcha-group">
          <img 
            v-if="captcha.image"
            :src="captcha.image" 
            alt="验证码" 
            class="captcha-image"
            @click="refreshCaptcha"
          />
          <button type="button" @click="refreshCaptcha">刷新</button>
        </div>
        <input
          v-model="formData.captchaCode"
          type="text"
          placeholder="请输入验证码"
          maxlength="4"
        />
      </div>

      <button type="submit" :disabled="loading" class="login-btn">
        {{ loading ? '登录中...' : '登录' }}
      </button>
    </form>

    <!-- 用户信息 -->
    <div v-else class="user-info">
      <h3>👤 用户信息</h3>
      <p><strong>ID:</strong> {{ user.id }}</p>
      <p><strong>手机号:</strong> {{ user.phone }}</p>
      <p><strong>用户名:</strong> {{ user.username }}</p>
      <p><strong>是否激活:</strong> {{ user.is_active ? '是' : '否' }}</p>
      <p><strong>管理员:</strong> {{ user.is_superuser ? '是' : '否' }}</p>
      <p><strong>创建时间:</strong> {{ formatDate(user.created_at) }}</p>
      
      <button @click="logout" class="logout-btn">退出登录</button>
    </div>

    <!-- 测试账户信息 -->
    <div class="test-accounts">
      <h4>测试账户:</h4>
      <p>管理员: *********** / admin123</p>
      <p>演示用户: *********** / demo123</p>
      <p>测试用户: *********** / test123</p>
    </div>
  </div>
</template>

<script>
import axios from 'axios'

const API_BASE = 'http://localhost:8000'

// 配置axios
axios.defaults.baseURL = API_BASE
axios.defaults.withCredentials = true

export default {
  name: 'LoginComponent',
  data() {
    return {
      formData: {
        phone: '',
        password: '',
        captchaCode: ''
      },
      captcha: {
        id: '',
        image: '',
        required: false
      },
      loginStatus: {
        remainingAttempts: 5,
        isLocked: false,
        requiresCaptcha: false
      },
      user: null,
      token: localStorage.getItem('access_token'),
      loading: false,
      message: { text: '', type: '' }
    }
  },
  
  mounted() {
    if (this.token) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${this.token}`
      this.getUserInfo()
    }
  },
  
  methods: {
    // 显示消息
    showMessage(text, type = 'info') {
      this.message = { text, type }
      setTimeout(() => {
        this.message = { text: '', type: '' }
      }, 5000)
    },

    // 检查登录状态
    async checkLoginStatus() {
      if (!this.formData.phone || this.formData.phone.length !== 11) return
      
      try {
        const response = await axios.get(`/api/v1/auth/login-attempts/${this.formData.phone}`)
        this.loginStatus = response.data
        
        if (response.data.requires_captcha) {
          await this.refreshCaptcha()
        }
      } catch (error) {
        this.showMessage('检查登录状态失败', 'error')
      }
    },

    // 刷新验证码
    async refreshCaptcha() {
      try {
        const response = await axios.get('/api/v1/auth/captcha')
        this.captcha = {
          id: response.data.captcha_id,
          image: response.data.captcha_image,
          required: true
        }
        this.showMessage('验证码已刷新', 'success')
      } catch (error) {
        this.showMessage('获取验证码失败', 'error')
      }
    },

    // 登录
    async handleLogin() {
      this.loading = true

      try {
        const loginData = {
          phone: this.formData.phone,
          password: this.formData.password
        }

        // 如果需要验证码，添加验证码数据
        if (this.loginStatus.requiresCaptcha && this.captcha.id && this.formData.captchaCode) {
          loginData.captcha_id = this.captcha.id
          loginData.captcha_code = this.formData.captchaCode
        }

        const response = await axios.post('/api/v1/auth/login', loginData)
        
        this.token = response.data.access_token
        localStorage.setItem('access_token', this.token)
        
        // 设置axios默认header
        axios.defaults.headers.common['Authorization'] = `Bearer ${this.token}`
        
        this.showMessage('登录成功！', 'success')
        this.resetForm()
        
        // 获取用户信息
        await this.getUserInfo()
        
      } catch (error) {
        const errorMsg = error.response?.data?.detail || '登录失败'
        this.showMessage(errorMsg, 'error')
        
        // 如果需要验证码，刷新验证码
        if (errorMsg.includes('验证码')) {
          await this.refreshCaptcha()
        }
      } finally {
        this.loading = false
      }
    },

    // 获取用户信息
    async getUserInfo() {
      if (!this.token) return

      try {
        const response = await axios.get('/api/v1/users/me', {
          headers: { Authorization: `Bearer ${this.token}` }
        })
        this.user = response.data
      } catch (error) {
        if (error.response?.status === 401) {
          this.logout()
        }
        this.showMessage('获取用户信息失败', 'error')
      }
    },

    // 退出登录
    logout() {
      this.token = null
      this.user = null
      localStorage.removeItem('access_token')
      delete axios.defaults.headers.common['Authorization']
      this.showMessage('已退出登录', 'info')
      this.resetForm()
    },

    // 重置表单
    resetForm() {
      this.formData = { phone: '', password: '', captchaCode: '' }
      this.captcha = { id: '', image: '', required: false }
      this.loginStatus = { remainingAttempts: 5, isLocked: false, requiresCaptcha: false }
    },

    // 格式化日期
    formatDate(dateString) {
      return new Date(dateString).toLocaleString()
    }
  }
}
</script>

<style scoped>
.login-container {
  max-width: 400px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.message {
  padding: 10px;
  margin-bottom: 20px;
  border-radius: 5px;
}

.message.error {
  background-color: #f8d7da;
  color: #721c24;
}

.message.success {
  background-color: #d4edda;
  color: #155724;
}

.message.info {
  background-color: #d1ecf1;
  color: #0c5460;
}

.status-info {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 5px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 16px;
  box-sizing: border-box;
}

.captcha-container {
  margin-bottom: 15px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  background-color: #f9f9f9;
}

.captcha-group {
  display: flex;
  align-items: center;
  margin: 10px 0;
}

.captcha-image {
  max-width: 120px;
  margin-right: 10px;
  cursor: pointer;
  border: 1px solid #ccc;
}

.login-btn {
  width: 100%;
  padding: 12px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
}

.login-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.login-btn:hover:not(:disabled) {
  background-color: #0056b3;
}

.user-info {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 5px;
}

.logout-btn {
  padding: 8px 16px;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.logout-btn:hover {
  background-color: #c82333;
}

.test-accounts {
  margin-top: 30px;
  font-size: 14px;
  color: #666;
  padding: 15px;
  background-color: #fff3cd;
  border-radius: 5px;
}

.test-accounts h4 {
  margin-top: 0;
  color: #856404;
}
</style>
