#!/usr/bin/env python3
"""
添加用户角色字段的数据库迁移脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from app.db.database import SessionLocal, engine
from app.db.models import Base


def add_role_column():
    """添加角色字段到用户表"""
    print("🔧 添加用户角色字段...")
    
    db = SessionLocal()
    try:
        # 检查角色字段是否已存在
        result = db.execute(text("""
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'role'
        """))
        
        if result.fetchone():
            print("✅ 角色字段已存在，跳过添加")
            return True
        
        # 添加角色字段
        print("📋 添加角色字段到用户表...")
        db.execute(text("""
            ALTER TABLE users 
            ADD COLUMN role ENUM('super_admin', 'admin', 'breeder', 'salesperson', 'user') 
            NOT NULL DEFAULT 'user' 
            COMMENT '用户角色'
        """))
        
        # 根据is_superuser字段设置初始角色
        print("📋 设置现有用户的角色...")
        db.execute(text("""
            UPDATE users 
            SET role = CASE 
                WHEN is_superuser = 1 THEN 'super_admin'
                ELSE 'user'
            END
        """))
        
        db.commit()
        print("✅ 角色字段添加成功")
        return True
        
    except Exception as e:
        print(f"❌ 添加角色字段失败: {e}")
        db.rollback()
        return False
    finally:
        db.close()


def verify_role_column():
    """验证角色字段"""
    print("\n🔍 验证角色字段...")
    
    db = SessionLocal()
    try:
        # 查询用户角色信息
        result = db.execute(text("""
            SELECT id, phone, username, role, is_superuser 
            FROM users 
            ORDER BY id
        """))
        
        users = result.fetchall()
        if users:
            print("📋 当前用户角色信息:")
            for user in users:
                print(f"   ID: {user.id}, 手机号: {user.phone}, 用户名: {user.username}, 角色: {user.role}, 超级用户: {user.is_superuser}")
        else:
            print("📋 暂无用户数据")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证角色字段失败: {e}")
        return False
    finally:
        db.close()


def main():
    """主函数"""
    print("🚀 用户角色字段迁移")
    print("=" * 50)
    
    # 检查数据库连接
    try:
        db = SessionLocal()
        db.execute(text("SELECT 1"))
        db.close()
        print("✅ 数据库连接正常")
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False
    
    # 添加角色字段
    if not add_role_column():
        return False
    
    # 验证角色字段
    if not verify_role_column():
        return False
    
    print("\n" + "=" * 50)
    print("🎉 用户角色字段迁移完成！")
    print("\n📋 支持的角色:")
    print("   - super_admin: 超级管理员")
    print("   - admin: 普通管理员")
    print("   - breeder: 饲养员")
    print("   - salesperson: 销售员")
    print("   - user: 普通用户")
    
    print("\n🔄 下一步:")
    print("   1. 重新初始化用户数据: python scripts/init_phone_users.py")
    print("   2. 重启服务: python main.py")
    print("   3. 测试用户信息: python scripts/test_user_info.py")
    
    return True


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
