"""
兔子类型相关的Pydantic模型
"""

from typing import Optional
from datetime import datetime
from decimal import Decimal
from pydantic import BaseModel, validator


class RabbitTypeBase(BaseModel):
    """兔子类型基础模型"""
    name: str
    default_unit: Optional[str] = None
    default_price: Optional[Decimal] = None
    remark: Optional[str] = None

    @validator('name')
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('类型名称不能为空')
        if len(v.strip()) > 50:
            raise ValueError('类型名称长度不能超过50个字符')
        return v.strip()

    @validator('default_unit')
    def validate_default_unit(cls, v):
        if v is not None:
            v = v.strip()
            if v and v not in ['只', '斤', '公斤', 'kg']:
                raise ValueError('默认单位只能是：只、斤、公斤、kg')
            return v if v else None
        return v

    @validator('default_price')
    def validate_default_price(cls, v):
        if v is not None:
            if v < 0:
                raise ValueError('默认单价不能为负数')
            if v > 99999.99:
                raise ValueError('默认单价不能超过99999.99元')
        return v

    @validator('remark')
    def validate_remark(cls, v):
        if v is not None and len(v.strip()) > 1000:
            raise ValueError('备注长度不能超过1000个字符')
        return v.strip() if v else None


class RabbitTypeCreate(RabbitTypeBase):
    """创建兔子类型模型"""
    pass


class RabbitTypeUpdate(BaseModel):
    """更新兔子类型模型"""
    name: Optional[str] = None
    default_unit: Optional[str] = None
    default_price: Optional[Decimal] = None
    remark: Optional[str] = None

    @validator('name')
    def validate_name(cls, v):
        if v is not None:
            if not v.strip():
                raise ValueError('类型名称不能为空')
            if len(v.strip()) > 50:
                raise ValueError('类型名称长度不能超过50个字符')
            return v.strip()
        return v

    @validator('default_unit')
    def validate_default_unit(cls, v):
        if v is not None:
            v = v.strip()
            if v and v not in ['只', '斤', '公斤', 'kg']:
                raise ValueError('默认单位只能是：只、斤、公斤、kg')
            return v if v else None
        return v

    @validator('default_price')
    def validate_default_price(cls, v):
        if v is not None:
            if v < 0:
                raise ValueError('默认单价不能为负数')
            if v > 99999.99:
                raise ValueError('默认单价不能超过99999.99元')
        return v

    @validator('remark')
    def validate_remark(cls, v):
        if v is not None and len(v.strip()) > 1000:
            raise ValueError('备注长度不能超过1000个字符')
        return v.strip() if v else None


class RabbitTypeInDBBase(RabbitTypeBase):
    """数据库中的兔子类型基础模型"""
    id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class RabbitType(RabbitTypeBase):
    """兔子类型模型（用于API响应）- 简化版，不包含时间戳"""
    id: int

    class Config:
        from_attributes = True


class RabbitTypeInDB(RabbitTypeInDBBase):
    """数据库中的兔子类型模型"""
    pass
