<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FastAPI 手机号登录演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .captcha-container {
            display: none;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            background: #f9f9f9;
            margin-bottom: 20px;
        }
        .captcha-image {
            max-width: 120px;
            border: 1px solid #ccc;
            margin-right: 10px;
            cursor: pointer;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .user-info {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
        }
        .attempts-info {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 FastAPI 手机号登录演示</h1>
        <p>演示跨域请求、验证码功能和JWT认证</p>
        
        <div class="attempts-info" id="attemptsInfo"></div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="phone">手机号:</label>
                <input type="tel" id="phone" placeholder="请输入11位手机号" maxlength="11" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" placeholder="请输入密码" required>
            </div>
            
            <div class="captcha-container" id="captchaContainer">
                <label>验证码:</label>
                <div style="display: flex; align-items: center; margin-bottom: 10px;">
                    <img id="captchaImage" class="captcha-image" alt="验证码" onclick="refreshCaptcha()">
                    <button type="button" onclick="refreshCaptcha()">刷新验证码</button>
                </div>
                <input type="text" id="captchaCode" placeholder="请输入验证码" maxlength="4">
                <input type="hidden" id="captchaId">
            </div>
            
            <button type="submit" id="loginBtn">登录</button>
            <button type="button" onclick="checkLoginStatus()">检查登录状态</button>
            <button type="button" onclick="getUserInfo()" id="userInfoBtn" disabled>获取用户信息</button>
        </form>
        
        <div id="result"></div>
        <div id="userInfo"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        let accessToken = localStorage.getItem('access_token');
        
        // 更新UI状态
        function updateUI() {
            const userInfoBtn = document.getElementById('userInfoBtn');
            if (accessToken) {
                userInfoBtn.disabled = false;
                getUserInfo();
            } else {
                userInfoBtn.disabled = true;
            }
        }
        
        // 显示结果
        function showResult(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = message;
        }
        
        // 检查登录状态
        async function checkLoginStatus() {
            const phone = document.getElementById('phone').value;
            if (!phone) {
                showResult('请先输入手机号', 'error');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/api/v1/auth/login-attempts/${phone}`);
                const data = await response.json();
                
                const attemptsInfo = document.getElementById('attemptsInfo');
                attemptsInfo.innerHTML = `
                    剩余尝试次数: ${data.remaining_attempts} | 
                    需要验证码: ${data.requires_captcha ? '是' : '否'} | 
                    账户状态: ${data.is_locked ? '已锁定' : '正常'}
                    ${data.locked_until ? `(锁定到: ${data.locked_until})` : ''}
                `;
                
                const captchaContainer = document.getElementById('captchaContainer');
                if (data.requires_captcha) {
                    captchaContainer.style.display = 'block';
                    await refreshCaptcha();
                } else {
                    captchaContainer.style.display = 'none';
                }
                
                showResult('登录状态检查完成', 'success');
            } catch (error) {
                showResult(`检查登录状态失败: ${error.message}`, 'error');
            }
        }
        
        // 刷新验证码
        async function refreshCaptcha() {
            try {
                const response = await fetch(`${API_BASE}/api/v1/auth/captcha`);
                const data = await response.json();
                
                document.getElementById('captchaImage').src = data.captcha_image;
                document.getElementById('captchaId').value = data.captcha_id;
                
                showResult('验证码已刷新', 'success');
            } catch (error) {
                showResult(`获取验证码失败: ${error.message}`, 'error');
            }
        }
        
        // 登录
        async function login(event) {
            event.preventDefault();
            
            const phone = document.getElementById('phone').value;
            const password = document.getElementById('password').value;
            const captchaId = document.getElementById('captchaId').value;
            const captchaCode = document.getElementById('captchaCode').value;
            
            const loginData = { phone, password };
            
            // 如果显示了验证码，添加验证码数据
            const captchaContainer = document.getElementById('captchaContainer');
            if (captchaContainer.style.display !== 'none' && captchaId && captchaCode) {
                loginData.captcha_id = captchaId;
                loginData.captcha_code = captchaCode;
            }
            
            try {
                const response = await fetch(`${API_BASE}/api/v1/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Origin': window.location.origin
                    },
                    body: JSON.stringify(loginData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    accessToken = data.access_token;
                    localStorage.setItem('access_token', accessToken);
                    showResult(`登录成功！Token: ${accessToken.substring(0, 50)}...`, 'success');
                    updateUI();
                    
                    // 清空表单
                    document.getElementById('loginForm').reset();
                    document.getElementById('captchaContainer').style.display = 'none';
                    document.getElementById('attemptsInfo').innerHTML = '';
                } else {
                    showResult(`登录失败: ${data.detail}`, 'error');
                    // 如果需要验证码，刷新验证码
                    if (data.detail.includes('验证码')) {
                        await refreshCaptcha();
                    }
                }
            } catch (error) {
                showResult(`登录请求失败: ${error.message}`, 'error');
            }
        }
        
        // 获取用户信息
        async function getUserInfo() {
            if (!accessToken) {
                showResult('请先登录', 'error');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/api/v1/users/me`, {
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Origin': window.location.origin
                    }
                });
                
                if (response.ok) {
                    const userData = await response.json();
                    const userInfoDiv = document.getElementById('userInfo');
                    userInfoDiv.className = 'user-info';
                    userInfoDiv.innerHTML = `
                        <h3>👤 用户信息</h3>
                        <p><strong>ID:</strong> ${userData.id}</p>
                        <p><strong>手机号:</strong> ${userData.phone}</p>
                        <p><strong>用户名:</strong> ${userData.username}</p>
                        <p><strong>是否激活:</strong> ${userData.is_active ? '是' : '否'}</p>
                        <p><strong>管理员:</strong> ${userData.is_superuser ? '是' : '否'}</p>
                        <p><strong>创建时间:</strong> ${new Date(userData.created_at).toLocaleString()}</p>
                        <button onclick="logout()">退出登录</button>
                    `;
                } else {
                    const errorData = await response.json();
                    showResult(`获取用户信息失败: ${errorData.detail}`, 'error');
                    if (response.status === 401) {
                        logout();
                    }
                }
            } catch (error) {
                showResult(`请求失败: ${error.message}`, 'error');
            }
        }
        
        // 退出登录
        async function logout() {
            if (!accessToken) {
                showResult('未登录', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/v1/auth/logout`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Origin': window.location.origin
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    showResult(`${data.message}`, 'success');
                } else {
                    showResult('退出登录失败', 'error');
                }
            } catch (error) {
                showResult(`退出登录请求失败: ${error.message}`, 'error');
            } finally {
                // 无论API调用是否成功，都清除本地token
                accessToken = null;
                localStorage.removeItem('access_token');
                document.getElementById('userInfo').innerHTML = '';
                updateUI();
            }
        }
        
        // 绑定事件
        document.getElementById('loginForm').addEventListener('submit', login);
        
        // 页面加载时检查登录状态
        window.addEventListener('load', () => {
            updateUI();
            showResult('页面加载完成，支持跨域请求', 'info');
        });
        
        // 手机号输入时自动检查状态
        document.getElementById('phone').addEventListener('blur', () => {
            const phone = document.getElementById('phone').value;
            if (phone && phone.length === 11) {
                checkLoginStatus();
            }
        });
    </script>
</body>
</html>
