#!/usr/bin/env python3
"""
初始化数据库表
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.db.database import engine
from app.db.models import Base, Customer, RabbitType

def init_tables():
    """创建所有表"""
    print("正在创建数据库表...")
    
    # 创建所有表
    Base.metadata.create_all(bind=engine)
    
    print("✅ 数据库表创建完成！")
    print("已创建的表:")
    print("- users (用户表)")
    print("- customers (客户表)")
    print("- rabbit_types (兔子类型表)")

if __name__ == "__main__":
    init_tables()
