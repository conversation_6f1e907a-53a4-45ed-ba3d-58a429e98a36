#!/usr/bin/env python3
"""
测试删除约束和错误处理
"""

import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_delete_constraints():
    print("🗑️ 测试删除约束和错误处理...")
    
    # 1. 登录
    print("\n1. 登录...")
    login_data = {
        "phone": "13800138000",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    if response.status_code == 200:
        token_data = response.json()
        headers = {"Authorization": f"Bearer {token_data['access_token']}"}
        print("✅ 登录成功")
    else:
        print(f"❌ 登录失败: {response.text}")
        return
    
    # 2. 创建测试数据
    print("\n2. 创建测试数据...")
    
    # 创建客户
    customer_data = {
        "name": "删除测试客户",
        "phone": "13900000004",
        "address": "测试地址",
        "remark": "用于测试删除约束"
    }
    
    response = requests.post(f"{BASE_URL}/customers/", json=customer_data, headers=headers)
    if response.status_code == 200:
        customer = response.json()
        print(f"✅ 创建客户成功: {customer['name']}")
        customer_id = customer['id']
    else:
        print(f"❌ 创建客户失败: {response.text}")
        return
    
    # 创建兔子类型
    rabbit_type_data = {
        "name": "删除测试兔",
        "remark": "用于测试删除约束"
    }
    
    response = requests.post(f"{BASE_URL}/rabbit-types/", json=rabbit_type_data, headers=headers)
    if response.status_code == 200:
        rabbit_type = response.json()
        print(f"✅ 创建兔子类型成功: {rabbit_type['name']}")
        rabbit_type_id = rabbit_type['id']
    else:
        print(f"❌ 创建兔子类型失败: {response.text}")
        return
    
    # 获取当前用户信息
    response = requests.get(f"{BASE_URL}/users/me", headers=headers)
    if response.status_code == 200:
        user_info = response.json()
        salesperson_id = user_info['id']
    else:
        print(f"❌ 获取用户信息失败: {response.text}")
        return
    
    # 创建订单（使用上面创建的客户和兔子类型）
    order_data = {
        "customer_id": customer_id,
        "salesperson_id": salesperson_id,
        "payment_method": "现金",
        "amount_paid": 100,
        "remark": "删除约束测试订单",
        "items": [
            {
                "rabbit_type_id": rabbit_type_id,
                "quantity": 5,
                "unit": "只",
                "unit_price": 25,
                "remark": "测试明细"
            }
        ]
    }
    
    response = requests.post(f"{BASE_URL}/orders/", json=order_data, headers=headers)
    if response.status_code == 200:
        order = response.json()
        print(f"✅ 创建订单成功: ID {order['id']}")
        order_id = order['id']
    else:
        print(f"❌ 创建订单失败: {response.text}")
        return
    
    # 3. 测试删除约束
    print("\n3. 测试删除约束...")
    
    # 3.1 尝试删除有订单明细引用的兔子类型
    print("   3.1 尝试删除有订单明细引用的兔子类型...")
    response = requests.delete(f"{BASE_URL}/rabbit-types/{rabbit_type_id}", headers=headers)
    if response.status_code == 400:
        error_info = response.json()
        print(f"✅ 正确阻止删除: {error_info['detail']}")
    else:
        print(f"❌ 删除约束失效: {response.status_code} - {response.text}")
    
    # 3.2 尝试删除有订单引用的客户
    print("   3.2 尝试删除有订单引用的客户...")
    response = requests.delete(f"{BASE_URL}/customers/{customer_id}", headers=headers)
    if response.status_code == 400:
        error_info = response.json()
        print(f"✅ 正确阻止删除: {error_info['detail']}")
    else:
        print(f"❌ 删除约束失效: {response.status_code} - {response.text}")
    
    # 4. 测试正确的删除顺序
    print("\n4. 测试正确的删除顺序...")
    
    # 4.1 先删除订单
    print("   4.1 删除订单...")
    response = requests.delete(f"{BASE_URL}/orders/{order_id}", headers=headers)
    if response.status_code == 200:
        delete_result = response.json()
        print(f"✅ 删除订单成功: {delete_result['message']}")
    else:
        print(f"❌ 删除订单失败: {response.text}")
    
    # 4.2 现在可以删除兔子类型
    print("   4.2 删除兔子类型...")
    response = requests.delete(f"{BASE_URL}/rabbit-types/{rabbit_type_id}", headers=headers)
    if response.status_code == 200:
        delete_result = response.json()
        print(f"✅ 删除兔子类型成功: {delete_result['message']}")
    else:
        print(f"❌ 删除兔子类型失败: {response.text}")
    
    # 4.3 现在可以删除客户
    print("   4.3 删除客户...")
    response = requests.delete(f"{BASE_URL}/customers/{customer_id}", headers=headers)
    if response.status_code == 200:
        delete_result = response.json()
        print(f"✅ 删除客户成功: {delete_result['message']}")
    else:
        print(f"❌ 删除客户失败: {response.text}")
    
    # 5. 测试删除不存在的记录
    print("\n5. 测试删除不存在的记录...")
    
    # 5.1 删除不存在的客户
    print("   5.1 删除不存在的客户...")
    response = requests.delete(f"{BASE_URL}/customers/99999", headers=headers)
    if response.status_code == 404:
        error_info = response.json()
        print(f"✅ 正确返回404: {error_info['detail']}")
    else:
        print(f"❌ 错误处理失效: {response.status_code}")
    
    # 5.2 删除不存在的兔子类型
    print("   5.2 删除不存在的兔子类型...")
    response = requests.delete(f"{BASE_URL}/rabbit-types/99999", headers=headers)
    if response.status_code == 404:
        error_info = response.json()
        print(f"✅ 正确返回404: {error_info['detail']}")
    else:
        print(f"❌ 错误处理失效: {response.status_code}")
    
    # 6. 测试创建可以删除的数据
    print("\n6. 测试创建可以直接删除的数据...")
    
    # 创建一个没有关联的客户
    standalone_customer_data = {
        "name": "可删除客户",
        "phone": "13900000005",
        "address": "测试地址",
        "remark": "没有订单关联的客户"
    }
    
    response = requests.post(f"{BASE_URL}/customers/", json=standalone_customer_data, headers=headers)
    if response.status_code == 200:
        standalone_customer = response.json()
        standalone_customer_id = standalone_customer['id']
        print(f"✅ 创建独立客户成功: {standalone_customer['name']}")
        
        # 直接删除
        response = requests.delete(f"{BASE_URL}/customers/{standalone_customer_id}", headers=headers)
        if response.status_code == 200:
            delete_result = response.json()
            print(f"✅ 直接删除成功: {delete_result['message']}")
        else:
            print(f"❌ 删除失败: {response.text}")
    else:
        print(f"❌ 创建独立客户失败: {response.text}")
    
    print("\n🎉 删除约束测试完成！")
    print("\n📋 测试总结:")
    print("   ✅ 外键约束正确工作，阻止了不安全的删除")
    print("   ✅ 错误信息详细，包含相关订单ID")
    print("   ✅ 正确的删除顺序可以成功删除")
    print("   ✅ 404错误正确处理不存在的记录")
    print("   ✅ 没有关联的记录可以直接删除")

if __name__ == "__main__":
    test_delete_constraints()
