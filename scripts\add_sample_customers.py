#!/usr/bin/env python3
"""
批量添加20个模拟客户信息
"""

import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

# 模拟客户数据
SAMPLE_CUSTOMERS = [
    {
        "name": "张三",
        "phone": "13800138001",
        "address": "北京市朝阳区建国路88号",
        "remark": "老客户，信誉良好，经常批量采购"
    },
    {
        "name": "李四",
        "phone": "13800138002", 
        "address": "上海市浦东新区陆家嘴金融中心",
        "remark": "新客户，主要采购种兔"
    },
    {
        "name": "王五",
        "phone": "13800138003",
        "address": "广州市天河区珠江新城",
        "remark": "餐饮客户，需要定期供应"
    },
    {
        "name": "赵六",
        "phone": "13800138004",
        "address": "深圳市南山区科技园",
        "remark": "批发商，量大价优"
    },
    {
        "name": "钱七",
        "phone": "13800138005",
        "address": "杭州市西湖区文三路",
        "remark": "电商客户，线上销售"
    },
    {
        "name": "孙八",
        "phone": "13800138006",
        "address": "南京市鼓楼区中山路",
        "remark": "农场主，自己养殖"
    },
    {
        "name": "周九",
        "phone": "13800138007",
        "address": "武汉市武昌区中南路",
        "remark": "合作社负责人"
    },
    {
        "name": "吴十",
        "phone": "13800138008",
        "address": "成都市锦江区春熙路",
        "remark": "川菜馆老板，需要新鲜兔肉"
    },
    {
        "name": "郑十一",
        "phone": "13800138009",
        "address": "西安市雁塔区小寨",
        "remark": "宠物店老板，主要采购观赏兔"
    },
    {
        "name": "王小明",
        "phone": "13800138010",
        "address": "重庆市渝中区解放碑",
        "remark": "火锅店采购，需要优质兔肉"
    },
    {
        "name": "李小红",
        "phone": "13800138011",
        "address": "天津市和平区南京路",
        "remark": "个人客户，家庭消费"
    },
    {
        "name": "张小华",
        "phone": "13800138012",
        "address": "青岛市市南区香港中路",
        "remark": "海鲜餐厅，偶尔采购兔肉"
    },
    {
        "name": "刘小强",
        "phone": "13800138013",
        "address": "大连市中山区人民路",
        "remark": "冷链物流公司，代理销售"
    },
    {
        "name": "陈小美",
        "phone": "13800138014",
        "address": "厦门市思明区中山路",
        "remark": "农贸市场摊主"
    },
    {
        "name": "林小刚",
        "phone": "13800138015",
        "address": "福州市鼓楼区五四路",
        "remark": "养殖技术顾问，采购种兔"
    },
    {
        "name": "黄小丽",
        "phone": "13800138016",
        "address": "长沙市岳麓区麓山南路",
        "remark": "湘菜馆老板娘"
    },
    {
        "name": "徐小伟",
        "phone": "13800138017",
        "address": "南昌市东湖区八一大道",
        "remark": "农产品经销商"
    },
    {
        "name": "朱小芳",
        "phone": "13800138018",
        "address": "合肥市蜀山区长江西路",
        "remark": "超市采购经理"
    },
    {
        "name": "马小军",
        "phone": "13800138019",
        "address": "石家庄市长安区中山路",
        "remark": "餐饮连锁店采购"
    },
    {
        "name": "高小燕",
        "phone": "13800138020",
        "address": "太原市迎泽区迎泽大街",
        "remark": "农村合作社采购员"
    }
]

def add_sample_customers():
    print("👥 开始批量添加20个模拟客户...")
    
    # 1. 登录获取token
    print("\n1. 登录获取token...")
    login_data = {"phone": "13800138000", "password": "admin123"}
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    
    if response.status_code == 200:
        token_data = response.json()
        headers = {"Authorization": f"Bearer {token_data['access_token']}"}
        print("✅ 登录成功")
    else:
        print(f"❌ 登录失败: {response.text}")
        return
    
    # 2. 批量添加客户
    print("\n2. 批量添加客户...")
    success_count = 0
    failed_count = 0
    
    for i, customer_data in enumerate(SAMPLE_CUSTOMERS, 1):
        print(f"   添加第{i}个客户: {customer_data['name']}...")
        
        response = requests.post(f"{BASE_URL}/customers/", json=customer_data, headers=headers)
        
        if response.status_code == 200:
            customer = response.json()
            print(f"   ✅ 成功: {customer['name']} (ID: {customer['id']})")
            success_count += 1
        else:
            print(f"   ❌ 失败: {customer_data['name']} - {response.text}")
            failed_count += 1
    
    # 3. 验证添加结果
    print("\n3. 验证添加结果...")
    response = requests.get(f"{BASE_URL}/customers/", headers=headers)
    
    if response.status_code == 200:
        customers = response.json()
        print(f"✅ 当前系统中共有 {len(customers)} 个客户")
        
        # 显示最新添加的客户
        print("\n最新添加的客户:")
        for customer in customers[-10:]:  # 显示最后10个
            print(f"   - {customer['name']} ({customer['phone']}) - {customer['address']}")
    else:
        print(f"❌ 获取客户列表失败: {response.text}")
    
    # 4. 统计结果
    print(f"\n📊 添加结果统计:")
    print(f"   ✅ 成功添加: {success_count} 个客户")
    print(f"   ❌ 添加失败: {failed_count} 个客户")
    print(f"   📱 电话号段: 13800138001 - 13800138020")
    print(f"   🏢 覆盖城市: 北京、上海、广州、深圳等20个城市")
    print(f"   👥 客户类型: 餐饮、批发、零售、养殖等多种类型")
    
    # 5. 按城市分组显示
    print(f"\n🏙️ 客户城市分布:")
    city_count = {}
    for customer in SAMPLE_CUSTOMERS:
        city = customer['address'].split('市')[0] + '市'
        city_count[city] = city_count.get(city, 0) + 1
    
    for city, count in city_count.items():
        print(f"   {city}: {count} 个客户")
    
    print("\n🎉 客户数据添加完成！")
    print("\n💡 这些客户数据可以用于:")
    print("   - 测试订单创建功能")
    print("   - 测试客户搜索功能") 
    print("   - 测试统计分析功能")
    print("   - 演示系统完整功能")

if __name__ == "__main__":
    add_sample_customers()
