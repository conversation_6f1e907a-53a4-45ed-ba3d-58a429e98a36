from fastapi import APIRouter
from app.api.v1.endpoints import auth, users, customers, rabbit_types, orders, permissions, statistics

api_router = APIRouter()
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(customers.router, prefix="/customers", tags=["customers"])
api_router.include_router(rabbit_types.router, prefix="/rabbit-types", tags=["rabbit-types"])
api_router.include_router(orders.router, prefix="/orders", tags=["orders"])
api_router.include_router(permissions.router, prefix="/permissions", tags=["permissions"])
api_router.include_router(statistics.router, prefix="/statistics", tags=["statistics"])
