"""
兔子类型管理API端点
"""

from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from app.api import deps
from app.crud.crud_rabbit_type import rabbit_type
from app.db.database import get_db
from app.db.models import User
from app.schemas.rabbit_type import RabbitType as RabbitTypeSchema, RabbitTypeCreate, RabbitTypeUpdate
from app.core.permissions import require_permission, Permission

router = APIRouter()


@router.get("/", response_model=List[RabbitTypeSchema])
def read_rabbit_types(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    order_by: str = Query("created_at", description="排序字段: name, created_at, updated_at"),
    search_name: Optional[str] = Query(None, description="按类型名称搜索"),
    current_user: User = Depends(require_permission(Permission.RABBIT_TYPE_READ)),
) -> Any:
    """
    获取兔子类型列表
    支持分页、排序和搜索
    """
    if search_name:
        rabbit_types = rabbit_type.search_by_name(db, name=search_name, skip=skip, limit=limit)
    else:
        rabbit_types = rabbit_type.get_multi_ordered(db, skip=skip, limit=limit, order_by=order_by)
    return rabbit_types


@router.get("/all", response_model=List[RabbitTypeSchema])
def read_all_rabbit_types(
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permission(Permission.RABBIT_TYPE_READ)),
) -> Any:
    """
    获取所有兔子类型（用于下拉选择等）
    """
    rabbit_types = rabbit_type.get_all_active(db)
    return rabbit_types


@router.post("/", response_model=RabbitTypeSchema)
def create_rabbit_type(
    *,
    db: Session = Depends(get_db),
    rabbit_type_in: RabbitTypeCreate,
    current_user: User = Depends(require_permission(Permission.RABBIT_TYPE_CREATE)),
) -> Any:
    """
    创建新兔子类型
    """
    # 检查是否已存在相同名称的类型
    existing_type = rabbit_type.get_by_name(db, name=rabbit_type_in.name)
    if existing_type:
        raise HTTPException(
            status_code=400,
            detail="该类型名称已存在",
        )
    
    new_rabbit_type = rabbit_type.create(db, obj_in=rabbit_type_in)
    return new_rabbit_type


@router.get("/{rabbit_type_id}", response_model=RabbitTypeSchema)
def read_rabbit_type(
    *,
    db: Session = Depends(get_db),
    rabbit_type_id: int,
    current_user: User = Depends(require_permission(Permission.RABBIT_TYPE_READ)),
) -> Any:
    """
    根据ID获取兔子类型信息
    """
    db_rabbit_type = rabbit_type.get(db, id=rabbit_type_id)
    if not db_rabbit_type:
        raise HTTPException(status_code=404, detail="兔子类型不存在")
    return db_rabbit_type


@router.put("/{rabbit_type_id}", response_model=RabbitTypeSchema)
def update_rabbit_type(
    *,
    db: Session = Depends(get_db),
    rabbit_type_id: int,
    rabbit_type_in: RabbitTypeUpdate,
    current_user: User = Depends(require_permission(Permission.RABBIT_TYPE_UPDATE)),
) -> Any:
    """
    更新兔子类型信息
    """
    db_rabbit_type = rabbit_type.get(db, id=rabbit_type_id)
    if not db_rabbit_type:
        raise HTTPException(status_code=404, detail="兔子类型不存在")
    
    # 如果更新名称，检查是否与其他类型冲突
    if rabbit_type_in.name and rabbit_type_in.name != db_rabbit_type.name:
        existing_type = rabbit_type.get_by_name(db, name=rabbit_type_in.name)
        if existing_type and existing_type.id != rabbit_type_id:
            raise HTTPException(
                status_code=400,
                detail="该类型名称已存在",
            )
    
    updated_rabbit_type = rabbit_type.update(db, db_obj=db_rabbit_type, obj_in=rabbit_type_in)
    return updated_rabbit_type


@router.delete("/{rabbit_type_id}")
def delete_rabbit_type(
    *,
    db: Session = Depends(get_db),
    rabbit_type_id: int,
    current_user: User = Depends(require_permission(Permission.RABBIT_TYPE_DELETE)),
) -> Any:
    """
    删除兔子类型
    """
    db_rabbit_type = rabbit_type.get(db, id=rabbit_type_id)
    if not db_rabbit_type:
        raise HTTPException(status_code=404, detail="兔子类型不存在")

    # 检查是否有订单明细引用了这个兔子类型
    from app.db.models import OrderItem, Order
    order_items_count = db.query(OrderItem).filter(OrderItem.rabbit_type_id == rabbit_type_id).count()
    if order_items_count > 0:
        # 获取相关订单信息
        related_orders = db.query(Order.id).join(OrderItem).filter(
            OrderItem.rabbit_type_id == rabbit_type_id
        ).distinct().limit(5).all()
        order_ids = [str(order.id) for order in related_orders]

        detail_msg = f"无法删除该兔子类型，因为有 {order_items_count} 个订单明细正在使用此类型。"
        if order_ids:
            detail_msg += f" 相关订单ID: {', '.join(order_ids)}"
            if len(related_orders) == 5:
                detail_msg += " 等"
        detail_msg += "。请先处理相关订单。"

        raise HTTPException(status_code=400, detail=detail_msg)

    rabbit_type.remove(db, id=rabbit_type_id)
    return {"message": "兔子类型删除成功"}
