#!/usr/bin/env python3
"""
测试权限控制系统
"""

import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_permissions():
    print("🔐 测试权限控制系统...")
    
    # 1. 使用管理员账户登录
    print("\n1. 管理员登录...")
    admin_login = {
        "phone": "13800138000",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=admin_login)
    if response.status_code == 200:
        admin_token_data = response.json()
        admin_headers = {"Authorization": f"Bearer {admin_token_data['access_token']}"}
        print("✅ 管理员登录成功")
    else:
        print(f"❌ 管理员登录失败: {response.text}")
        return
    
    # 2. 查看管理员权限
    print("\n2. 查看管理员权限...")
    response = requests.get(f"{BASE_URL}/permissions/my-permissions", headers=admin_headers)
    if response.status_code == 200:
        admin_perms = response.json()
        print(f"✅ 管理员角色: {admin_perms['role']['name']}")
        print(f"   权限数量: {admin_perms['total_permissions']}")
        print("   主要权限:")
        for perm in admin_perms['permissions'][:5]:  # 显示前5个权限
            print(f"   - {perm['description']} ({perm['code']})")
    else:
        print(f"❌ 获取管理员权限失败: {response.text}")
    
    # 3. 查看所有角色和权限
    print("\n3. 查看所有角色和权限...")
    response = requests.get(f"{BASE_URL}/permissions/roles", headers=admin_headers)
    if response.status_code == 200:
        roles_info = response.json()
        print(f"✅ 系统共有 {roles_info['total_roles']} 个角色:")
        for role_code, role_info in roles_info['roles'].items():
            print(f"   - {role_info['name']} ({role_code}): {len(role_info['permissions'])} 个权限")
    else:
        print(f"❌ 获取角色信息失败: {response.text}")
    
    # 4. 测试管理员可以访问的功能
    print("\n4. 测试管理员权限...")
    
    # 用户管理（只有管理员可以）
    response = requests.get(f"{BASE_URL}/users/", headers=admin_headers)
    if response.status_code == 200:
        users = response.json()
        print(f"✅ 管理员可以查看用户列表: {len(users)} 个用户")
    else:
        print(f"❌ 管理员查看用户列表失败: {response.text}")
    
    # 客户管理
    response = requests.get(f"{BASE_URL}/customers/", headers=admin_headers)
    if response.status_code == 200:
        customers = response.json()
        print(f"✅ 管理员可以查看客户列表: {len(customers)} 个客户")
    else:
        print(f"❌ 管理员查看客户列表失败: {response.text}")
    
    # 订单管理
    response = requests.get(f"{BASE_URL}/orders/", headers=admin_headers)
    if response.status_code == 200:
        orders = response.json()
        print(f"✅ 管理员可以查看所有订单: {len(orders)} 个订单")
    else:
        print(f"❌ 管理员查看订单失败: {response.text}")
    
    # 5. 创建一个销售员用户来测试权限限制
    print("\n5. 创建销售员用户...")
    salesperson_data = {
        "phone": "13900000001",  # 使用固定的有效手机号
        "username": "测试销售员",
        "password": "sales123",
        "role": "salesperson",
        "is_active": True
    }
    
    response = requests.post(f"{BASE_URL}/users/", json=salesperson_data, headers=admin_headers)
    if response.status_code == 200:
        salesperson = response.json()
        print(f"✅ 创建销售员成功: {salesperson['username']}")
        salesperson_phone = salesperson['phone']
    else:
        print(f"❌ 创建销售员失败: {response.text}")
        return
    
    # 6. 销售员登录
    print("\n6. 销售员登录...")
    sales_login = {
        "phone": "13900000001",  # 使用固定手机号
        "password": "sales123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=sales_login)
    if response.status_code == 200:
        sales_token_data = response.json()
        sales_headers = {"Authorization": f"Bearer {sales_token_data['access_token']}"}
        print("✅ 销售员登录成功")
    else:
        print(f"❌ 销售员登录失败: {response.text}")
        return
    
    # 7. 查看销售员权限
    print("\n7. 查看销售员权限...")
    response = requests.get(f"{BASE_URL}/permissions/my-permissions", headers=sales_headers)
    if response.status_code == 200:
        sales_perms = response.json()
        print(f"✅ 销售员角色: {sales_perms['role']['name']}")
        print(f"   权限数量: {sales_perms['total_permissions']}")
        print("   权限列表:")
        for perm in sales_perms['permissions']:
            print(f"   - {perm['description']}")
    else:
        print(f"❌ 获取销售员权限失败: {response.text}")
    
    # 8. 测试销售员权限限制
    print("\n8. 测试销售员权限限制...")
    
    # 销售员不能查看用户列表
    response = requests.get(f"{BASE_URL}/users/", headers=sales_headers)
    if response.status_code == 403:
        print("✅ 销售员无法查看用户列表（权限正确）")
    else:
        print(f"❌ 销售员可以查看用户列表（权限错误）: {response.status_code}")
    
    # 销售员可以查看客户列表
    response = requests.get(f"{BASE_URL}/customers/", headers=sales_headers)
    if response.status_code == 200:
        print("✅ 销售员可以查看客户列表（权限正确）")
    else:
        print(f"❌ 销售员无法查看客户列表（权限错误）: {response.text}")
    
    # 销售员可以查看订单（但只能看自己的）
    response = requests.get(f"{BASE_URL}/orders/", headers=sales_headers)
    if response.status_code == 200:
        sales_orders = response.json()
        print(f"✅ 销售员可以查看订单: {len(sales_orders)} 个（只能看自己的）")
    else:
        print(f"❌ 销售员无法查看订单: {response.text}")
    
    # 销售员不能删除客户
    if customers:  # 如果有客户数据
        customer_id = customers[0]['id']
        response = requests.delete(f"{BASE_URL}/customers/{customer_id}", headers=sales_headers)
        if response.status_code == 403:
            print("✅ 销售员无法删除客户（权限正确）")
        else:
            print(f"❌ 销售员可以删除客户（权限错误）: {response.status_code}")
    
    # 9. 测试权限检查API
    print("\n9. 测试权限检查API...")
    
    # 检查销售员是否有创建客户的权限
    response = requests.get(f"{BASE_URL}/permissions/check/customer:create", headers=sales_headers)
    if response.status_code == 200:
        check_result = response.json()
        print(f"✅ 权限检查: 销售员{'有' if check_result['has_permission'] else '没有'}创建客户的权限")
    
    # 检查销售员是否有删除用户的权限
    response = requests.get(f"{BASE_URL}/permissions/check/user:delete", headers=sales_headers)
    if response.status_code == 200:
        check_result = response.json()
        print(f"✅ 权限检查: 销售员{'有' if check_result['has_permission'] else '没有'}删除用户的权限")
    
    print("\n🎉 权限控制系统测试完成！")
    print("\n📋 测试总结:")
    print("   ✅ 基于角色的访问控制（RBAC）正常工作")
    print("   ✅ 管理员拥有完整权限")
    print("   ✅ 销售员权限受到正确限制")
    print("   ✅ 权限检查API正常工作")
    print("   ✅ 403错误正确返回给无权限用户")

if __name__ == "__main__":
    test_permissions()
