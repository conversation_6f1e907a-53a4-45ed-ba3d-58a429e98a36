"""
客户管理API端点
"""

from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from app.api import deps
from app.crud.crud_customer import customer
from app.db.database import get_db
from app.db.models import User
from app.schemas.customer import Customer as CustomerSchema, CustomerCreate, CustomerUpdate
from app.core.permissions import require_permission, Permission

router = APIRouter()


@router.get("/", response_model=List[CustomerSchema])
def read_customers(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    order_by: str = Query("created_at", description="排序字段: name, phone, created_at, updated_at"),
    search_name: Optional[str] = Query(None, description="按姓名搜索"),
    search_phone: Optional[str] = Query(None, description="按电话搜索"),
    current_user: User = Depends(require_permission(Permission.CUSTOMER_READ)),
) -> Any:
    """
    获取客户列表
    支持分页、排序和搜索
    """
    if search_name:
        customers = customer.search_by_name(db, name=search_name, skip=skip, limit=limit)
    elif search_phone:
        customers = customer.search_by_phone(db, phone=search_phone, skip=skip, limit=limit)
    else:
        customers = customer.get_multi_ordered(db, skip=skip, limit=limit, order_by=order_by)
    return customers


@router.post("/", response_model=CustomerSchema)
def create_customer(
    *,
    db: Session = Depends(get_db),
    customer_in: CustomerCreate,
    current_user: User = Depends(require_permission(Permission.CUSTOMER_CREATE)),
) -> Any:
    """
    创建新客户
    """
    # 检查是否已存在相同电话的客户
    existing_customer = customer.get_by_phone(db, phone=customer_in.phone)
    if existing_customer:
        raise HTTPException(
            status_code=400,
            detail="该电话号码已被其他客户使用",
        )
    
    new_customer = customer.create(db, obj_in=customer_in)
    return new_customer


@router.get("/{customer_id}", response_model=CustomerSchema)
def read_customer(
    *,
    db: Session = Depends(get_db),
    customer_id: int,
    current_user: User = Depends(require_permission(Permission.CUSTOMER_READ)),
) -> Any:
    """
    根据ID获取客户信息
    """
    db_customer = customer.get(db, id=customer_id)
    if not db_customer:
        raise HTTPException(status_code=404, detail="客户不存在")
    return db_customer


@router.put("/{customer_id}", response_model=CustomerSchema)
def update_customer(
    *,
    db: Session = Depends(get_db),
    customer_id: int,
    customer_in: CustomerUpdate,
    current_user: User = Depends(require_permission(Permission.CUSTOMER_UPDATE)),
) -> Any:
    """
    更新客户信息
    """
    db_customer = customer.get(db, id=customer_id)
    if not db_customer:
        raise HTTPException(status_code=404, detail="客户不存在")
    
    # 如果更新电话号码，检查是否与其他客户冲突
    if customer_in.phone and customer_in.phone != db_customer.phone:
        existing_customer = customer.get_by_phone(db, phone=customer_in.phone)
        if existing_customer and existing_customer.id != customer_id:
            raise HTTPException(
                status_code=400,
                detail="该电话号码已被其他客户使用",
            )
    
    updated_customer = customer.update(db, db_obj=db_customer, obj_in=customer_in)
    return updated_customer


@router.delete("/{customer_id}")
def delete_customer(
    *,
    db: Session = Depends(get_db),
    customer_id: int,
    current_user: User = Depends(require_permission(Permission.CUSTOMER_DELETE)),
) -> Any:
    """
    删除客户
    """
    db_customer = customer.get(db, id=customer_id)
    if not db_customer:
        raise HTTPException(status_code=404, detail="客户不存在")

    # 检查是否有订单引用了这个客户
    from app.db.models import Order
    orders_count = db.query(Order).filter(Order.customer_id == customer_id).count()
    if orders_count > 0:
        # 获取相关订单信息
        related_orders = db.query(Order.id).filter(
            Order.customer_id == customer_id
        ).limit(5).all()
        order_ids = [str(order.id) for order in related_orders]

        detail_msg = f"无法删除该客户，因为有 {orders_count} 个订单记录关联此客户。"
        if order_ids:
            detail_msg += f" 相关订单ID: {', '.join(order_ids)}"
            if len(related_orders) == 5:
                detail_msg += " 等"
        detail_msg += "。请先处理相关订单。"

        raise HTTPException(status_code=400, detail=detail_msg)

    customer.remove(db, id=customer_id)
    return {"message": "客户删除成功"}
