import React, { useState, useEffect } from 'react';
import axios from 'axios';

const API_BASE = 'http://localhost:8000';

// 配置axios默认设置
axios.defaults.baseURL = API_BASE;
axios.defaults.withCredentials = true;

const LoginComponent = () => {
  const [formData, setFormData] = useState({
    phone: '',
    password: '',
    captchaCode: ''
  });
  
  const [captcha, setCaptcha] = useState({
    id: '',
    image: '',
    required: false
  });
  
  const [loginStatus, setLoginStatus] = useState({
    remainingAttempts: 5,
    isLocked: false,
    requiresCaptcha: false
  });
  
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(localStorage.getItem('access_token'));
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState({ text: '', type: '' });

  // 显示消息
  const showMessage = (text, type = 'info') => {
    setMessage({ text, type });
    setTimeout(() => setMessage({ text: '', type: '' }), 5000);
  };

  // 检查登录状态
  const checkLoginStatus = async (phone) => {
    if (!phone || phone.length !== 11) return;
    
    try {
      const response = await axios.get(`/api/v1/auth/login-attempts/${phone}`);
      setLoginStatus(response.data);
      
      if (response.data.requires_captcha) {
        await refreshCaptcha();
      }
    } catch (error) {
      showMessage('检查登录状态失败', 'error');
    }
  };

  // 刷新验证码
  const refreshCaptcha = async () => {
    try {
      const response = await axios.get('/api/v1/auth/captcha');
      setCaptcha({
        id: response.data.captcha_id,
        image: response.data.captcha_image,
        required: true
      });
      showMessage('验证码已刷新', 'success');
    } catch (error) {
      showMessage('获取验证码失败', 'error');
    }
  };

  // 登录
  const handleLogin = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const loginData = {
        phone: formData.phone,
        password: formData.password
      };

      // 如果需要验证码，添加验证码数据
      if (loginStatus.requiresCaptcha && captcha.id && formData.captchaCode) {
        loginData.captcha_id = captcha.id;
        loginData.captcha_code = formData.captchaCode;
      }

      const response = await axios.post('/api/v1/auth/login', loginData);
      
      const accessToken = response.data.access_token;
      setToken(accessToken);
      localStorage.setItem('access_token', accessToken);
      
      // 设置axios默认header
      axios.defaults.headers.common['Authorization'] = `Bearer ${accessToken}`;
      
      showMessage('登录成功！', 'success');
      setFormData({ phone: '', password: '', captchaCode: '' });
      setCaptcha({ id: '', image: '', required: false });
      
      // 获取用户信息
      await getUserInfo();
      
    } catch (error) {
      const errorMsg = error.response?.data?.detail || '登录失败';
      showMessage(errorMsg, 'error');
      
      // 如果需要验证码，刷新验证码
      if (errorMsg.includes('验证码')) {
        await refreshCaptcha();
      }
    } finally {
      setLoading(false);
    }
  };

  // 获取用户信息
  const getUserInfo = async () => {
    if (!token) return;

    try {
      const response = await axios.get('/api/v1/users/me', {
        headers: { Authorization: `Bearer ${token}` }
      });
      setUser(response.data);
    } catch (error) {
      if (error.response?.status === 401) {
        logout();
      }
      showMessage('获取用户信息失败', 'error');
    }
  };

  // 退出登录
  const logout = () => {
    setToken(null);
    setUser(null);
    localStorage.removeItem('access_token');
    delete axios.defaults.headers.common['Authorization'];
    showMessage('已退出登录', 'info');
  };

  // 处理表单输入
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // 手机号输入完成时检查状态
    if (name === 'phone' && value.length === 11) {
      checkLoginStatus(value);
    }
  };

  // 页面加载时检查token
  useEffect(() => {
    if (token) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      getUserInfo();
    }
  }, [token]);

  return (
    <div style={{ maxWidth: '400px', margin: '0 auto', padding: '20px' }}>
      <h2>🚀 FastAPI 登录</h2>
      
      {/* 消息显示 */}
      {message.text && (
        <div style={{
          padding: '10px',
          marginBottom: '20px',
          borderRadius: '5px',
          backgroundColor: message.type === 'error' ? '#f8d7da' : 
                          message.type === 'success' ? '#d4edda' : '#d1ecf1',
          color: message.type === 'error' ? '#721c24' : 
                 message.type === 'success' ? '#155724' : '#0c5460'
        }}>
          {message.text}
        </div>
      )}

      {/* 登录状态信息 */}
      {formData.phone.length === 11 && (
        <div style={{ fontSize: '14px', color: '#666', marginBottom: '15px' }}>
          剩余尝试: {loginStatus.remainingAttempts} | 
          需要验证码: {loginStatus.requiresCaptcha ? '是' : '否'} | 
          状态: {loginStatus.isLocked ? '已锁定' : '正常'}
        </div>
      )}

      {!user ? (
        <form onSubmit={handleLogin}>
          {/* 手机号输入 */}
          <div style={{ marginBottom: '15px' }}>
            <label>手机号:</label>
            <input
              type="tel"
              name="phone"
              value={formData.phone}
              onChange={handleInputChange}
              placeholder="请输入11位手机号"
              maxLength="11"
              required
              style={{ width: '100%', padding: '8px', marginTop: '5px' }}
            />
          </div>

          {/* 密码输入 */}
          <div style={{ marginBottom: '15px' }}>
            <label>密码:</label>
            <input
              type="password"
              name="password"
              value={formData.password}
              onChange={handleInputChange}
              placeholder="请输入密码"
              required
              style={{ width: '100%', padding: '8px', marginTop: '5px' }}
            />
          </div>

          {/* 验证码 */}
          {loginStatus.requiresCaptcha && (
            <div style={{ marginBottom: '15px', padding: '15px', border: '1px solid #ddd', borderRadius: '5px' }}>
              <label>验证码:</label>
              <div style={{ display: 'flex', alignItems: 'center', marginTop: '5px', marginBottom: '10px' }}>
                {captcha.image && (
                  <img 
                    src={captcha.image} 
                    alt="验证码" 
                    style={{ maxWidth: '120px', marginRight: '10px', cursor: 'pointer' }}
                    onClick={refreshCaptcha}
                  />
                )}
                <button type="button" onClick={refreshCaptcha}>
                  刷新
                </button>
              </div>
              <input
                type="text"
                name="captchaCode"
                value={formData.captchaCode}
                onChange={handleInputChange}
                placeholder="请输入验证码"
                maxLength="4"
                style={{ width: '100%', padding: '8px' }}
              />
            </div>
          )}

          <button 
            type="submit" 
            disabled={loading}
            style={{
              width: '100%',
              padding: '12px',
              backgroundColor: loading ? '#ccc' : '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: loading ? 'not-allowed' : 'pointer'
            }}
          >
            {loading ? '登录中...' : '登录'}
          </button>
        </form>
      ) : (
        <div style={{ padding: '20px', backgroundColor: '#f8f9fa', borderRadius: '5px' }}>
          <h3>👤 用户信息</h3>
          <p><strong>ID:</strong> {user.id}</p>
          <p><strong>手机号:</strong> {user.phone}</p>
          <p><strong>用户名:</strong> {user.username}</p>
          <p><strong>是否激活:</strong> {user.is_active ? '是' : '否'}</p>
          <p><strong>管理员:</strong> {user.is_superuser ? '是' : '否'}</p>
          <p><strong>创建时间:</strong> {new Date(user.created_at).toLocaleString()}</p>
          
          <button 
            onClick={logout}
            style={{
              padding: '8px 16px',
              backgroundColor: '#dc3545',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer'
            }}
          >
            退出登录
          </button>
        </div>
      )}

      {/* 测试账户信息 */}
      <div style={{ marginTop: '30px', fontSize: '14px', color: '#666' }}>
        <h4>测试账户:</h4>
        <p>管理员: 13800138000 / admin123</p>
        <p>演示用户: 13900139000 / demo123</p>
        <p>测试用户: 15000150000 / test123</p>
      </div>
    </div>
  );
};

export default LoginComponent;
