"""
客户CRUD操作
"""

from typing import List, Optional
from sqlalchemy.orm import Session
from app.crud.base import CRUDBase
from app.db.models import Customer
from app.schemas.customer import CustomerCreate, CustomerUpdate


class CRUDCustomer(CRUDBase[Customer, CustomerCreate, CustomerUpdate]):
    """客户CRUD操作类"""

    def get_by_phone(self, db: Session, *, phone: str) -> Optional[Customer]:
        """根据电话号码获取客户"""
        return db.query(Customer).filter(Customer.phone == phone).first()

    def get_by_name(self, db: Session, *, name: str) -> Optional[Customer]:
        """根据姓名获取客户"""
        return db.query(Customer).filter(Customer.name == name).first()

    def search_by_name(self, db: Session, *, name: str, skip: int = 0, limit: int = 100) -> List[Customer]:
        """根据姓名模糊搜索客户"""
        return (
            db.query(Customer)
            .filter(Customer.name.contains(name))
            .offset(skip)
            .limit(limit)
            .all()
        )

    def search_by_phone(self, db: Session, *, phone: str, skip: int = 0, limit: int = 100) -> List[Customer]:
        """根据电话号码模糊搜索客户"""
        return (
            db.query(Customer)
            .filter(Customer.phone.contains(phone))
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_multi_ordered(
        self, db: Session, *, skip: int = 0, limit: int = 100, order_by: str = "created_at"
    ) -> List[Customer]:
        """获取多个客户，支持排序"""
        query = db.query(Customer)
        
        if order_by == "name":
            query = query.order_by(Customer.name)
        elif order_by == "phone":
            query = query.order_by(Customer.phone)
        elif order_by == "updated_at":
            query = query.order_by(Customer.updated_at.desc())
        else:  # 默认按创建时间倒序
            query = query.order_by(Customer.created_at.desc())
            
        return query.offset(skip).limit(limit).all()


# 创建实例
customer = CRUDCustomer(Customer)
