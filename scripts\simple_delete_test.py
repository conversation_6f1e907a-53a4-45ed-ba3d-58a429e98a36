#!/usr/bin/env python3
"""
简单的删除测试
"""

import requests

BASE_URL = "http://localhost:8000/api/v1"

def simple_delete_test():
    print("🗑️ 简单删除测试...")
    
    # 1. 登录
    print("\n1. 登录...")
    login_data = {
        "phone": "13800138000",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    if response.status_code == 200:
        token_data = response.json()
        headers = {"Authorization": f"Bearer {token_data['access_token']}"}
        print("✅ 登录成功")
    else:
        print(f"❌ 登录失败: {response.text}")
        return
    
    # 2. 创建一个可以删除的兔子类型
    print("\n2. 创建可删除的兔子类型...")
    rabbit_type_data = {
        "name": "可删除测试兔",
        "remark": "这个类型没有订单关联，可以删除"
    }
    
    response = requests.post(f"{BASE_URL}/rabbit-types/", json=rabbit_type_data, headers=headers)
    if response.status_code == 200:
        rabbit_type = response.json()
        print(f"✅ 创建兔子类型成功: {rabbit_type['name']}")
        rabbit_type_id = rabbit_type['id']
    else:
        print(f"❌ 创建兔子类型失败: {response.text}")
        return
    
    # 3. 删除这个兔子类型
    print("\n3. 删除兔子类型...")
    response = requests.delete(f"{BASE_URL}/rabbit-types/{rabbit_type_id}", headers=headers)
    if response.status_code == 200:
        delete_result = response.json()
        print(f"✅ 删除成功: {delete_result['message']}")
    else:
        print(f"❌ 删除失败: {response.status_code} - {response.text}")
    
    # 4. 尝试删除有订单关联的兔子类型
    print("\n4. 尝试删除有订单关联的兔子类型...")
    
    # 获取现有的兔子类型
    response = requests.get(f"{BASE_URL}/rabbit-types/", headers=headers)
    if response.status_code == 200:
        rabbit_types = response.json()
        if rabbit_types:
            existing_type_id = rabbit_types[0]['id']
            print(f"   尝试删除兔子类型ID: {existing_type_id}")
            
            response = requests.delete(f"{BASE_URL}/rabbit-types/{existing_type_id}", headers=headers)
            if response.status_code == 400:
                error_info = response.json()
                print(f"✅ 正确阻止删除: {error_info['detail']}")
            elif response.status_code == 200:
                print("✅ 删除成功（该类型没有关联订单）")
            else:
                print(f"❌ 意外错误: {response.status_code} - {response.text}")
        else:
            print("   没有现有的兔子类型可测试")
    else:
        print(f"❌ 获取兔子类型列表失败: {response.text}")
    
    print("\n🎉 简单删除测试完成！")

if __name__ == "__main__":
    simple_delete_test()
