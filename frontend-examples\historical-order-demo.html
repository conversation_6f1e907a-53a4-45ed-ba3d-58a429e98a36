<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>历史订单创建演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        .datetime-input {
            background-color: #f9f9f9;
        }
        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 30px;
        }
        button {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .btn-current {
            background-color: #4CAF50;
            color: white;
        }
        .btn-current:hover {
            background-color: #45a049;
        }
        .btn-historical {
            background-color: #2196F3;
            color: white;
        }
        .btn-historical:hover {
            background-color: #1976D2;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            color: #0c5460;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📅 历史订单创建演示</h1>
        
        <div class="info">
            <strong>功能说明：</strong><br>
            • 不指定时间：创建当前时间的订单<br>
            • 指定时间：创建历史订单，用于补录过去的销售数据<br>
            • 时间限制：不能是未来时间，不能早于2020年
        </div>

        <form id="orderForm">
            <div class="form-group">
                <label for="customerSelect">客户：</label>
                <select id="customerSelect" required>
                    <option value="">请选择客户</option>
                </select>
            </div>

            <div class="form-group">
                <label for="rabbitTypeSelect">兔子类型：</label>
                <select id="rabbitTypeSelect" required>
                    <option value="">请选择兔子类型</option>
                </select>
            </div>

            <div class="form-group">
                <label for="quantity">数量：</label>
                <input type="number" id="quantity" step="0.1" min="0.1" value="10" required>
            </div>

            <div class="form-group">
                <label for="unit">单位：</label>
                <select id="unit" required>
                    <option value="只">只</option>
                    <option value="斤">斤</option>
                    <option value="公斤">公斤</option>
                </select>
            </div>

            <div class="form-group">
                <label for="unitPrice">单价：</label>
                <input type="number" id="unitPrice" step="0.01" min="0.01" value="30" required>
            </div>

            <div class="form-group">
                <label for="amountPaid">已付金额：</label>
                <input type="number" id="amountPaid" step="0.01" min="0" value="300" required>
            </div>

            <div class="form-group">
                <label for="paymentMethod">付款方式：</label>
                <select id="paymentMethod" required>
                    <option value="现金">现金</option>
                    <option value="微信">微信</option>
                    <option value="支付宝">支付宝</option>
                    <option value="银行转账">银行转账</option>
                </select>
            </div>

            <div class="form-group">
                <label for="createdAt">订单时间（可选）：</label>
                <input type="datetime-local" id="createdAt" class="datetime-input" 
                       title="不填写则使用当前时间，填写则创建历史订单">
            </div>

            <div class="form-group">
                <label for="remark">备注：</label>
                <textarea id="remark" rows="3" placeholder="订单备注信息"></textarea>
            </div>

            <div class="button-group">
                <button type="button" class="btn-current" onclick="createCurrentOrder()">
                    创建当前订单
                </button>
                <button type="button" class="btn-historical" onclick="createHistoricalOrder()">
                    创建历史订单
                </button>
            </div>
        </form>

        <div id="result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/v1';
        let authToken = '';

        // 页面加载时初始化
        window.onload = async function() {
            await login();
            await loadCustomers();
            await loadRabbitTypes();
        };

        // 登录获取token
        async function login() {
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        phone: '13800138000',
                        password: 'admin123'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    authToken = data.access_token;
                    showResult('登录成功', 'success');
                } else {
                    showResult('登录失败', 'error');
                }
            } catch (error) {
                showResult(`登录错误: ${error.message}`, 'error');
            }
        }

        // 加载客户列表
        async function loadCustomers() {
            try {
                const response = await fetch(`${API_BASE}/customers/`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                if (response.ok) {
                    const customers = await response.json();
                    const select = document.getElementById('customerSelect');
                    customers.forEach(customer => {
                        const option = document.createElement('option');
                        option.value = customer.id;
                        option.textContent = `${customer.name} (${customer.phone})`;
                        select.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('加载客户失败:', error);
            }
        }

        // 加载兔子类型列表
        async function loadRabbitTypes() {
            try {
                const response = await fetch(`${API_BASE}/rabbit-types/all`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                if (response.ok) {
                    const rabbitTypes = await response.json();
                    const select = document.getElementById('rabbitTypeSelect');
                    rabbitTypes.forEach(type => {
                        const option = document.createElement('option');
                        option.value = type.id;
                        option.textContent = type.name;
                        select.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('加载兔子类型失败:', error);
            }
        }

        // 创建当前时间订单
        async function createCurrentOrder() {
            const orderData = getOrderData();
            delete orderData.created_at; // 不指定时间，使用当前时间
            await createOrder(orderData, '当前时间订单');
        }

        // 创建历史订单
        async function createHistoricalOrder() {
            const orderData = getOrderData();
            const createdAt = document.getElementById('createdAt').value;
            
            if (!createdAt) {
                showResult('请选择历史订单时间', 'error');
                return;
            }
            
            orderData.created_at = new Date(createdAt).toISOString();
            await createOrder(orderData, '历史订单');
        }

        // 获取表单数据
        function getOrderData() {
            return {
                customer_id: parseInt(document.getElementById('customerSelect').value),
                salesperson_id: 1, // 假设当前用户ID为1
                amount_paid: parseFloat(document.getElementById('amountPaid').value),
                payment_method: document.getElementById('paymentMethod').value,
                remark: document.getElementById('remark').value,
                items: [{
                    rabbit_type_id: parseInt(document.getElementById('rabbitTypeSelect').value),
                    quantity: parseFloat(document.getElementById('quantity').value),
                    unit: document.getElementById('unit').value,
                    unit_price: parseFloat(document.getElementById('unitPrice').value),
                    remark: '演示订单明细'
                }]
            };
        }

        // 创建订单
        async function createOrder(orderData, orderType) {
            try {
                const response = await fetch(`${API_BASE}/orders/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(orderData)
                });

                if (response.ok) {
                    const order = await response.json();
                    showResult(`${orderType}创建成功！订单ID: ${order.id}，总价: ${order.total_price}元`, 'success');
                } else {
                    const error = await response.json();
                    showResult(`${orderType}创建失败: ${error.detail}`, 'error');
                }
            } catch (error) {
                showResult(`${orderType}创建错误: ${error.message}`, 'error');
            }
        }

        // 显示结果
        function showResult(message, type) {
            const result = document.getElementById('result');
            result.textContent = message;
            result.className = `result ${type}`;
            result.style.display = 'block';
            
            // 3秒后自动隐藏
            setTimeout(() => {
                result.style.display = 'none';
            }, 3000);
        }
    </script>
</body>
</html>
