"""
权限管理API端点
"""

from typing import Any, List, Dict
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from app.api import deps
from app.db.database import get_db
from app.db.models import User, UserRole
from app.core.permissions import (
    require_permission, Permission, ROLE_PERMISSIONS,
    get_user_permissions, get_permission_description, get_role_description,
    require_admin
)

router = APIRouter()


@router.get("/roles", response_model=Dict[str, Any])
def get_roles_and_permissions(
    current_user: User = Depends(require_admin()),
) -> Any:
    """
    获取所有角色和权限信息（管理员功能）
    """
    roles_info = {}
    
    for role, permissions in ROLE_PERMISSIONS.items():
        roles_info[role.value] = {
            "name": get_role_description(role),
            "permissions": [
                {
                    "code": perm,
                    "description": get_permission_description(perm)
                }
                for perm in permissions
            ]
        }
    
    return {
        "roles": roles_info,
        "total_roles": len(ROLE_PERMISSIONS),
        "available_permissions": [
            {
                "code": perm,
                "description": get_permission_description(perm)
            }
            for perm in [
                Permission.USER_READ, Permission.USER_CREATE, Permission.USER_UPDATE, Permission.USER_DELETE,
                Permission.CUSTOMER_READ, Permission.CUSTOMER_CREATE, Permission.CUSTOMER_UPDATE, Permission.CUSTOMER_DELETE,
                Permission.RABBIT_TYPE_READ, Permission.RABBIT_TYPE_CREATE, Permission.RABBIT_TYPE_UPDATE, Permission.RABBIT_TYPE_DELETE,
                Permission.ORDER_READ, Permission.ORDER_CREATE, Permission.ORDER_UPDATE, Permission.ORDER_DELETE,
                Permission.ORDER_READ_ALL, Permission.ORDER_PAYMENT, Permission.SYSTEM_ADMIN,
            ]
        ]
    }


@router.get("/user/{user_id}", response_model=Dict[str, Any])
def get_user_permissions_info(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin()),
) -> Any:
    """
    获取指定用户的权限信息（管理员功能）
    """
    from app.crud.crud_user import user as user_crud
    
    target_user = user_crud.get(db, id=user_id)
    if not target_user:
        from fastapi import HTTPException, status
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    user_permissions = get_user_permissions(target_user)
    
    return {
        "user_id": target_user.id,
        "username": target_user.username,
        "phone": target_user.phone,
        "role": {
            "code": target_user.role.value,
            "name": get_role_description(target_user.role)
        },
        "permissions": [
            {
                "code": perm,
                "description": get_permission_description(perm)
            }
            for perm in user_permissions
        ],
        "total_permissions": len(user_permissions)
    }


@router.get("/my-permissions", response_model=Dict[str, Any])
def get_my_permissions(
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取当前用户的权限信息
    """
    user_permissions = get_user_permissions(current_user)
    
    return {
        "user_id": current_user.id,
        "username": current_user.username,
        "role": {
            "code": current_user.role.value,
            "name": get_role_description(current_user.role)
        },
        "permissions": [
            {
                "code": perm,
                "description": get_permission_description(perm)
            }
            for perm in user_permissions
        ],
        "total_permissions": len(user_permissions)
    }


@router.get("/check/{permission}")
def check_permission(
    permission: str,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    检查当前用户是否有指定权限
    """
    from app.core.permissions import has_permission
    
    has_perm = has_permission(current_user, permission)
    
    return {
        "user_id": current_user.id,
        "permission": permission,
        "permission_description": get_permission_description(permission),
        "has_permission": has_perm,
        "user_role": current_user.role.value
    }
