#!/usr/bin/env python3
"""
测试历史订单创建功能
"""

import requests
import json
from datetime import datetime, timedelta

BASE_URL = "http://localhost:8000/api/v1"

def test_historical_orders():
    print("📅 测试历史订单创建功能...")
    
    # 1. 登录
    print("\n1. 登录...")
    login_data = {"phone": "13800138000", "password": "admin123"}
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    
    if response.status_code == 200:
        token_data = response.json()
        headers = {"Authorization": f"Bearer {token_data['access_token']}"}
        print("✅ 登录成功")
    else:
        print(f"❌ 登录失败: {response.text}")
        return
    
    # 获取用户信息
    response = requests.get(f"{BASE_URL}/users/me", headers=headers)
    user_info = response.json()
    salesperson_id = user_info['id']
    
    # 获取客户和兔子类型
    customers = requests.get(f"{BASE_URL}/customers/", headers=headers).json()
    rabbit_types = requests.get(f"{BASE_URL}/rabbit-types/all", headers=headers).json()
    
    if not customers or not rabbit_types:
        print("❌ 需要先创建客户和兔子类型")
        return
    
    customer_id = customers[0]['id']
    rabbit_type_id = rabbit_types[0]['id']
    
    # 2. 创建当前时间的订单（不指定created_at）
    print("\n2. 创建当前时间的订单...")
    current_order_data = {
        "customer_id": customer_id,
        "salesperson_id": salesperson_id,
        "payment_method": "现金",
        "amount_paid": 300,
        "remark": "当前时间订单",
        "items": [
            {
                "rabbit_type_id": rabbit_type_id,
                "quantity": 10,
                "unit": "只",
                "unit_price": 35,
                "remark": "当前订单明细"
            }
        ]
        # 不指定 created_at，使用当前时间
    }
    
    response = requests.post(f"{BASE_URL}/orders/", json=current_order_data, headers=headers)
    if response.status_code == 200:
        current_order = response.json()
        print(f"✅ 当前订单创建成功: ID {current_order['id']}")
        print(f"   创建时间: {current_order.get('created_at', '未显示')}")
    else:
        print(f"❌ 当前订单创建失败: {response.text}")
    
    # 3. 创建昨天的历史订单
    print("\n3. 创建昨天的历史订单...")
    yesterday = datetime.now() - timedelta(days=1)
    yesterday_order_data = {
        "customer_id": customer_id,
        "salesperson_id": salesperson_id,
        "payment_method": "微信",
        "amount_paid": 500,
        "remark": "昨天的历史订单",
        "created_at": yesterday.isoformat(),  # 指定昨天的时间
        "items": [
            {
                "rabbit_type_id": rabbit_type_id,
                "quantity": 15,
                "unit": "只",
                "unit_price": 40,
                "remark": "昨天的订单明细"
            }
        ]
    }
    
    response = requests.post(f"{BASE_URL}/orders/", json=yesterday_order_data, headers=headers)
    if response.status_code == 200:
        yesterday_order = response.json()
        print(f"✅ 昨天订单创建成功: ID {yesterday_order['id']}")
        print(f"   指定时间: {yesterday.strftime('%Y-%m-%d %H:%M:%S')}")
    else:
        print(f"❌ 昨天订单创建失败: {response.text}")
    
    # 4. 创建上周的历史订单
    print("\n4. 创建上周的历史订单...")
    last_week = datetime.now() - timedelta(days=7)
    last_week_order_data = {
        "customer_id": customer_id,
        "salesperson_id": salesperson_id,
        "payment_method": "支付宝",
        "amount_paid": 800,
        "remark": "上周的历史订单",
        "created_at": last_week.isoformat(),  # 指定上周的时间
        "items": [
            {
                "rabbit_type_id": rabbit_type_id,
                "quantity": 20,
                "unit": "只",
                "unit_price": 45,
                "remark": "上周的订单明细"
            }
        ]
    }
    
    response = requests.post(f"{BASE_URL}/orders/", json=last_week_order_data, headers=headers)
    if response.status_code == 200:
        last_week_order = response.json()
        print(f"✅ 上周订单创建成功: ID {last_week_order['id']}")
        print(f"   指定时间: {last_week.strftime('%Y-%m-%d %H:%M:%S')}")
    else:
        print(f"❌ 上周订单创建失败: {response.text}")
    
    # 5. 测试无效时间（未来时间）
    print("\n5. 测试无效时间（未来时间）...")
    future_time = datetime.now() + timedelta(days=1)
    future_order_data = {
        "customer_id": customer_id,
        "salesperson_id": salesperson_id,
        "payment_method": "现金",
        "amount_paid": 100,
        "remark": "未来时间订单（应该失败）",
        "created_at": future_time.isoformat(),  # 未来时间
        "items": [
            {
                "rabbit_type_id": rabbit_type_id,
                "quantity": 5,
                "unit": "只",
                "unit_price": 30,
                "remark": "未来订单明细"
            }
        ]
    }
    
    response = requests.post(f"{BASE_URL}/orders/", json=future_order_data, headers=headers)
    if response.status_code == 422:
        error_data = response.json()
        print(f"✅ 未来时间正确被拒绝: {error_data.get('detail', '验证失败')}")
    else:
        print(f"❌ 未来时间应该被拒绝，但创建成功了: {response.status_code}")
    
    # 6. 测试过早时间（2019年）
    print("\n6. 测试过早时间（2019年）...")
    too_early = datetime(2019, 1, 1, 12, 0, 0)
    early_order_data = {
        "customer_id": customer_id,
        "salesperson_id": salesperson_id,
        "payment_method": "现金",
        "amount_paid": 100,
        "remark": "过早时间订单（应该失败）",
        "created_at": too_early.isoformat(),  # 2019年
        "items": [
            {
                "rabbit_type_id": rabbit_type_id,
                "quantity": 5,
                "unit": "只",
                "unit_price": 30,
                "remark": "过早订单明细"
            }
        ]
    }
    
    response = requests.post(f"{BASE_URL}/orders/", json=early_order_data, headers=headers)
    if response.status_code == 422:
        error_data = response.json()
        print(f"✅ 过早时间正确被拒绝: {error_data.get('detail', '验证失败')}")
    else:
        print(f"❌ 过早时间应该被拒绝，但创建成功了: {response.status_code}")
    
    # 7. 查看订单列表，验证时间排序
    print("\n7. 查看订单列表（按时间排序）...")
    response = requests.get(f"{BASE_URL}/orders/?order_by=created_at", headers=headers)
    if response.status_code == 200:
        orders = response.json()
        print("✅ 订单列表（按创建时间排序）:")
        for order in orders[-5:]:  # 显示最近5个订单
            created_at = order.get('created_at', '未知时间')
            print(f"   订单ID {order['id']}: {created_at} - {order['remark']}")
    else:
        print(f"❌ 获取订单列表失败: {response.text}")
    
    print("\n🎉 历史订单测试完成！")
    print("\n📋 功能总结:")
    print("   ✅ 支持创建当前时间订单（不指定created_at）")
    print("   ✅ 支持创建历史订单（指定created_at）")
    print("   ✅ 验证时间范围（不能是未来时间或过早时间）")
    print("   ✅ 可以按时间排序查看订单")
    print("   ✅ 适用于补录历史销售数据")

if __name__ == "__main__":
    test_historical_orders()
