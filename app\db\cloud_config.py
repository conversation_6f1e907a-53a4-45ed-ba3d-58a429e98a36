"""
云数据库配置
专门用于云服务器MySQL数据库的配置和连接管理
"""

import pymysql
from sqlalchemy import create_engine
from sqlalchemy.pool import QueuePool
from app.core.config import settings


class CloudDatabaseConfig:
    """云数据库配置类"""
    
    # 云数据库连接信息
    HOST = "lp.beiluling.cn"
    PORT = 3306
    DATABASE = "lptcgl"
    USERNAME = "lptcgl"
    PASSWORD = "lptcgl"
    
    @classmethod
    def get_database_url(cls):
        """获取数据库连接URL"""
        return f"mysql+pymysql://{cls.USERNAME}:{cls.PASSWORD}@{cls.HOST}:{cls.PORT}/{cls.DATABASE}"
    
    @classmethod
    def get_engine_config(cls):
        """获取云数据库引擎配置"""
        return {
            'poolclass': QueuePool,
            'pool_size': 5,          # 云数据库连接池较小
            'max_overflow': 10,      # 最大溢出连接
            'pool_pre_ping': True,   # 连接前检查
            'pool_recycle': 1800,    # 30分钟回收连接（云数据库通常有连接时间限制）
            'connect_args': {
                'charset': 'utf8mb4',
                'autocommit': False,
                'connect_timeout': 10,  # 连接超时10秒
                'read_timeout': 30,     # 读取超时30秒
                'write_timeout': 30,    # 写入超时30秒
            }
        }
    
    @classmethod
    def test_connection(cls):
        """测试云数据库连接"""
        try:
            connection = pymysql.connect(
                host=cls.HOST,
                port=cls.PORT,
                user=cls.USERNAME,
                password=cls.PASSWORD,
                database=cls.DATABASE,
                charset='utf8mb4',
                connect_timeout=10
            )
            
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                
            connection.close()
            return True, "连接成功"
            
        except pymysql.Error as e:
            return False, f"连接失败: {e}"
        except Exception as e:
            return False, f"未知错误: {e}"
    
    @classmethod
    def get_database_info(cls):
        """获取数据库信息"""
        try:
            connection = pymysql.connect(
                host=cls.HOST,
                port=cls.PORT,
                user=cls.USERNAME,
                password=cls.PASSWORD,
                database=cls.DATABASE,
                charset='utf8mb4'
            )
            
            info = {}
            with connection.cursor() as cursor:
                # 获取MySQL版本
                cursor.execute("SELECT VERSION()")
                info['mysql_version'] = cursor.fetchone()[0]
                
                # 获取当前数据库
                cursor.execute("SELECT DATABASE()")
                info['current_database'] = cursor.fetchone()[0]
                
                # 获取字符集
                cursor.execute("SHOW VARIABLES LIKE 'character_set_database'")
                result = cursor.fetchone()
                info['charset'] = result[1] if result else 'unknown'
                
                # 获取时区
                cursor.execute("SELECT @@time_zone")
                info['timezone'] = cursor.fetchone()[0]
                
                # 获取表列表
                cursor.execute("SHOW TABLES")
                tables = cursor.fetchall()
                info['tables'] = [table[0] for table in tables]
            
            connection.close()
            return True, info
            
        except Exception as e:
            return False, f"获取数据库信息失败: {e}"


def create_cloud_engine():
    """创建云数据库引擎"""
    config = CloudDatabaseConfig()
    database_url = config.get_database_url()
    engine_config = config.get_engine_config()
    
    return create_engine(database_url, **engine_config)
