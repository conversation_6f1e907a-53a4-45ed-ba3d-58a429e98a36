"""
基于角色的访问控制（RBAC）系统
"""

from typing import List, Optional
from fastapi import HTTPException, status, Depends
from app.db.models import User, UserRole
from app.api import deps


class Permission:
    """权限类"""
    
    # 用户管理权限
    USER_READ = "user:read"
    USER_CREATE = "user:create"
    USER_UPDATE = "user:update"
    USER_DELETE = "user:delete"
    
    # 客户管理权限
    CUSTOMER_READ = "customer:read"
    CUSTOMER_CREATE = "customer:create"
    CUSTOMER_UPDATE = "customer:update"
    CUSTOMER_DELETE = "customer:delete"
    
    # 兔子类型管理权限
    RABBIT_TYPE_READ = "rabbit_type:read"
    RABBIT_TYPE_CREATE = "rabbit_type:create"
    RABBIT_TYPE_UPDATE = "rabbit_type:update"
    RABBIT_TYPE_DELETE = "rabbit_type:delete"
    
    # 订单管理权限
    ORDER_READ = "order:read"
    ORDER_CREATE = "order:create"
    ORDER_UPDATE = "order:update"
    ORDER_DELETE = "order:delete"
    ORDER_READ_ALL = "order:read_all"  # 查看所有人的订单
    ORDER_PAYMENT = "order:payment"
    
    # 系统管理权限
    SYSTEM_ADMIN = "system:admin"


# 角色权限映射
ROLE_PERMISSIONS = {
    UserRole.SUPER_ADMIN: [
        # 超级管理员拥有所有权限
        Permission.USER_READ, Permission.USER_CREATE, Permission.USER_UPDATE, Permission.USER_DELETE,
        Permission.CUSTOMER_READ, Permission.CUSTOMER_CREATE, Permission.CUSTOMER_UPDATE, Permission.CUSTOMER_DELETE,
        Permission.RABBIT_TYPE_READ, Permission.RABBIT_TYPE_CREATE, Permission.RABBIT_TYPE_UPDATE, Permission.RABBIT_TYPE_DELETE,
        Permission.ORDER_READ, Permission.ORDER_CREATE, Permission.ORDER_UPDATE, Permission.ORDER_DELETE,
        Permission.ORDER_READ_ALL, Permission.ORDER_PAYMENT,
        Permission.SYSTEM_ADMIN,
    ],
    
    UserRole.ADMIN: [
        # 普通管理员：用户管理受限，其他基本权限
        Permission.USER_READ,  # 只能查看用户，不能创建/修改/删除
        Permission.CUSTOMER_READ, Permission.CUSTOMER_CREATE, Permission.CUSTOMER_UPDATE, Permission.CUSTOMER_DELETE,
        Permission.RABBIT_TYPE_READ, Permission.RABBIT_TYPE_CREATE, Permission.RABBIT_TYPE_UPDATE, Permission.RABBIT_TYPE_DELETE,
        Permission.ORDER_READ, Permission.ORDER_CREATE, Permission.ORDER_UPDATE, Permission.ORDER_DELETE,
        Permission.ORDER_READ_ALL, Permission.ORDER_PAYMENT,
    ],
    
    UserRole.SALESPERSON: [
        # 销售员：主要负责客户和订单管理
        Permission.CUSTOMER_READ, Permission.CUSTOMER_CREATE, Permission.CUSTOMER_UPDATE,  # 不能删除客户
        Permission.RABBIT_TYPE_READ,  # 只能查看兔子类型，不能修改
        Permission.ORDER_READ, Permission.ORDER_CREATE, Permission.ORDER_UPDATE, Permission.ORDER_PAYMENT,
        # 注意：销售员不能删除订单，也不能查看所有人的订单（只能看自己的）
    ],
    
    UserRole.BREEDER: [
        # 饲养员：主要负责兔子类型管理，查看客户和订单
        Permission.CUSTOMER_READ,  # 只能查看客户
        Permission.RABBIT_TYPE_READ, Permission.RABBIT_TYPE_CREATE, Permission.RABBIT_TYPE_UPDATE,
        Permission.ORDER_READ,  # 只能查看订单，不能创建/修改
    ],
}


def get_user_permissions(user: User) -> List[str]:
    """获取用户的所有权限"""
    return ROLE_PERMISSIONS.get(user.role, [])


def has_permission(user: User, permission: str) -> bool:
    """检查用户是否有指定权限"""
    user_permissions = get_user_permissions(user)
    return permission in user_permissions


def require_permission(permission: str):
    """权限装饰器：要求用户具有指定权限"""
    def permission_checker(current_user: User = Depends(deps.get_current_active_user)):
        if not has_permission(current_user, permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"权限不足：需要 {permission} 权限",
            )
        return current_user
    return permission_checker


def require_permissions(permissions: List[str]):
    """权限装饰器：要求用户具有指定的多个权限之一"""
    def permission_checker(current_user: User = Depends(deps.get_current_active_user)):
        user_permissions = get_user_permissions(current_user)
        if not any(perm in user_permissions for perm in permissions):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"权限不足：需要以下权限之一 {permissions}",
            )
        return current_user
    return permission_checker


def require_role(role: UserRole):
    """角色装饰器：要求用户具有指定角色"""
    def role_checker(current_user: User = Depends(deps.get_current_active_user)):
        if current_user.role != role:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"权限不足：需要 {role.value} 角色",
            )
        return current_user
    return role_checker


def require_roles(roles: List[UserRole]):
    """角色装饰器：要求用户具有指定角色之一"""
    def role_checker(current_user: User = Depends(deps.get_current_active_user)):
        if current_user.role not in roles:
            role_names = [role.value for role in roles]
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"权限不足：需要以下角色之一 {role_names}",
            )
        return current_user
    return role_checker


def require_admin():
    """管理员权限装饰器"""
    return require_roles([UserRole.SUPER_ADMIN, UserRole.ADMIN])


def require_super_admin():
    """超级管理员权限装饰器"""
    return require_role(UserRole.SUPER_ADMIN)


def can_access_user_data(current_user: User, target_user_id: int) -> bool:
    """检查用户是否可以访问指定用户的数据"""
    # 超级管理员和普通管理员可以访问所有用户数据
    if current_user.role in [UserRole.SUPER_ADMIN, UserRole.ADMIN]:
        return True
    
    # 普通用户只能访问自己的数据
    return current_user.id == target_user_id


def can_access_order_data(current_user: User, order_salesperson_id: Optional[int] = None) -> bool:
    """检查用户是否可以访问指定订单数据"""
    # 有查看所有订单权限的用户（管理员）
    if has_permission(current_user, Permission.ORDER_READ_ALL):
        return True
    
    # 销售员只能查看自己的订单
    if order_salesperson_id is not None:
        return current_user.id == order_salesperson_id
    
    # 其他情况需要有基本的订单读取权限
    return has_permission(current_user, Permission.ORDER_READ)


def get_permission_description(permission: str) -> str:
    """获取权限的中文描述"""
    descriptions = {
        Permission.USER_READ: "查看用户信息",
        Permission.USER_CREATE: "创建用户",
        Permission.USER_UPDATE: "修改用户信息",
        Permission.USER_DELETE: "删除用户",
        Permission.CUSTOMER_READ: "查看客户信息",
        Permission.CUSTOMER_CREATE: "创建客户",
        Permission.CUSTOMER_UPDATE: "修改客户信息",
        Permission.CUSTOMER_DELETE: "删除客户",
        Permission.RABBIT_TYPE_READ: "查看兔子类型",
        Permission.RABBIT_TYPE_CREATE: "创建兔子类型",
        Permission.RABBIT_TYPE_UPDATE: "修改兔子类型",
        Permission.RABBIT_TYPE_DELETE: "删除兔子类型",
        Permission.ORDER_READ: "查看订单",
        Permission.ORDER_CREATE: "创建订单",
        Permission.ORDER_UPDATE: "修改订单",
        Permission.ORDER_DELETE: "删除订单",
        Permission.ORDER_READ_ALL: "查看所有订单",
        Permission.ORDER_PAYMENT: "订单付款",
        Permission.SYSTEM_ADMIN: "系统管理",
    }
    return descriptions.get(permission, permission)


def get_role_description(role: UserRole) -> str:
    """获取角色的中文描述"""
    descriptions = {
        UserRole.SUPER_ADMIN: "超级管理员",
        UserRole.ADMIN: "普通管理员",
        UserRole.SALESPERSON: "销售员",
        UserRole.BREEDER: "饲养员",
    }
    return descriptions.get(role, role.value)
