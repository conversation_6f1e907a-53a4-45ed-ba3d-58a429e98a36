"""
销售订单CRUD操作
"""

from typing import List, Optional
from decimal import Decimal
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, desc
from app.crud.base import CRUDBase
from app.db.models import Order, OrderItem, OrderStatus, Customer, User, RabbitType
from app.schemas.order import OrderCreate, OrderUpdate


class CRUDOrder(CRUDBase[Order, OrderCreate, OrderUpdate]):
    """销售订单CRUD操作类"""

    def create_with_items(self, db: Session, *, obj_in: OrderCreate) -> Order:
        """创建订单及其明细"""
        # 计算订单总价
        total_price = Decimal('0')
        for item in obj_in.items:
            item_total = item.quantity * item.unit_price
            total_price += item_total

        # 计算未付金额
        unpaid_amount = total_price - obj_in.amount_paid

        # 确定订单状态
        if obj_in.amount_paid == 0:
            status = OrderStatus.PENDING
        elif obj_in.amount_paid >= total_price:
            status = OrderStatus.PAID
            unpaid_amount = Decimal('0')
        else:
            status = OrderStatus.PARTIAL_PAID

        # 创建订单主记录
        order_data = {
            "customer_id": obj_in.customer_id,
            "salesperson_id": obj_in.salesperson_id,
            "total_price": total_price,
            "amount_paid": obj_in.amount_paid,
            "unpaid_amount": unpaid_amount,
            "payment_method": obj_in.payment_method,
            "status": status,
            "remark": obj_in.remark,
        }

        # 如果指定了创建时间，则使用指定时间
        if obj_in.created_at:
            order_data["created_at"] = obj_in.created_at

        db_order = Order(**order_data)
        db.add(db_order)
        db.flush()  # 获取订单ID

        # 创建订单明细
        for item in obj_in.items:
            item_total = item.quantity * item.unit_price
            order_item = OrderItem(
                order_id=db_order.id,
                rabbit_type_id=item.rabbit_type_id,
                quantity=item.quantity,
                unit=item.unit,
                unit_price=item.unit_price,
                total_price=item_total,
                remark=item.remark,
            )
            db.add(order_item)

        db.commit()
        db.refresh(db_order)
        return db_order

    def get_with_details(self, db: Session, id: int) -> Optional[Order]:
        """获取订单及其详细信息"""
        return (
            db.query(Order)
            .options(
                joinedload(Order.customer),
                joinedload(Order.salesperson),
                joinedload(Order.items).joinedload(OrderItem.rabbit_type)
            )
            .filter(Order.id == id)
            .first()
        )

    def get_multi_with_details(
        self, 
        db: Session, 
        *, 
        skip: int = 0, 
        limit: int = 100,
        customer_id: Optional[int] = None,
        salesperson_id: Optional[int] = None,
        status: Optional[OrderStatus] = None,
        order_by: str = "created_at"
    ) -> List[Order]:
        """获取订单列表及详细信息"""
        query = (
            db.query(Order)
            .options(
                joinedload(Order.customer),
                joinedload(Order.salesperson)
            )
        )

        # 添加过滤条件
        if customer_id:
            query = query.filter(Order.customer_id == customer_id)
        if salesperson_id:
            query = query.filter(Order.salesperson_id == salesperson_id)
        if status:
            query = query.filter(Order.status == status)

        # 排序
        if order_by == "total_price":
            query = query.order_by(desc(Order.total_price))
        elif order_by == "customer_name":
            query = query.join(Customer).order_by(Customer.name)
        elif order_by == "salesperson_name":
            query = query.join(User).order_by(User.username)
        else:  # 默认按创建时间倒序
            query = query.order_by(desc(Order.created_at))

        return query.offset(skip).limit(limit).all()

    def search_orders(
        self, 
        db: Session, 
        *, 
        search_term: str, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[Order]:
        """搜索订单（按客户姓名或销售员姓名）"""
        return (
            db.query(Order)
            .join(Customer)
            .join(User)
            .options(
                joinedload(Order.customer),
                joinedload(Order.salesperson)
            )
            .filter(
                or_(
                    Customer.name.contains(search_term),
                    User.username.contains(search_term)
                )
            )
            .order_by(desc(Order.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_customer(
        self, 
        db: Session, 
        *, 
        customer_id: int, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[Order]:
        """获取指定客户的订单"""
        return (
            db.query(Order)
            .options(joinedload(Order.salesperson))
            .filter(Order.customer_id == customer_id)
            .order_by(desc(Order.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_salesperson(
        self, 
        db: Session, 
        *, 
        salesperson_id: int, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[Order]:
        """获取指定销售员的订单"""
        return (
            db.query(Order)
            .options(joinedload(Order.customer))
            .filter(Order.salesperson_id == salesperson_id)
            .order_by(desc(Order.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_unpaid_orders(self, db: Session, *, skip: int = 0, limit: int = 100) -> List[Order]:
        """获取有欠款的订单"""
        return (
            db.query(Order)
            .options(
                joinedload(Order.customer),
                joinedload(Order.salesperson)
            )
            .filter(Order.unpaid_amount > 0)
            .order_by(desc(Order.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )

    def update_payment(
        self, 
        db: Session, 
        *, 
        order_id: int, 
        additional_payment: Decimal,
        payment_method: Optional[str] = None
    ) -> Optional[Order]:
        """更新订单付款"""
        order = db.query(Order).filter(Order.id == order_id).first()
        if not order:
            return None

        # 更新付款金额
        order.amount_paid += additional_payment
        if payment_method:
            order.payment_method = payment_method

        # 重新计算未付金额和状态
        order.unpaid_amount = order.total_price - order.amount_paid
        
        if order.amount_paid >= order.total_price:
            order.status = OrderStatus.PAID
            order.unpaid_amount = Decimal('0')
        elif order.amount_paid > 0:
            order.status = OrderStatus.PARTIAL_PAID
        else:
            order.status = OrderStatus.PENDING

        db.commit()
        db.refresh(order)
        return order


# 创建实例
order = CRUDOrder(Order)
