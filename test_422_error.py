#!/usr/bin/env python3
"""
测试422错误的具体内容
"""

import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_422_error():
    print("🔍 测试422错误详情...")
    
    # 1. 登录获取token
    login_data = {"phone": "13800138000", "password": "admin123"}
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    token = response.json()['access_token']
    headers = {"Authorization": f"Bearer {token}"}
    
    # 2. 故意发送错误的订单数据来触发422错误
    print("发送错误的订单数据...")
    
    # 错误数据示例1：缺少必需字段
    bad_order_data = {
        "customer_id": 1,
        # 缺少 salesperson_id
        "payment_method": "微信",
        "amount_paid": 500,
        "items": [
            {
                "rabbit_type_id": 1,
                "quantity": 10,
                # 缺少 unit
                "unit_price": 30
            }
        ]
    }
    
    response = requests.post(f"{BASE_URL}/orders/", json=bad_order_data, headers=headers)
    
    print(f"状态码: {response.status_code}")
    print(f"Content-Type: {response.headers.get('content-type')}")
    print(f"响应内容: {response.text}")
    
    if response.status_code == 422:
        try:
            error_data = response.json()
            print(f"\n解析后的错误数据:")
            print(json.dumps(error_data, ensure_ascii=False, indent=2))
            
            if 'detail' in error_data:
                print(f"\n具体验证错误:")
                for i, error in enumerate(error_data['detail'], 1):
                    print(f"  错误{i}: {error}")
                    if isinstance(error, dict):
                        print(f"    字段: {error.get('loc', 'unknown')}")
                        print(f"    消息: {error.get('msg', 'unknown')}")
                        print(f"    类型: {error.get('type', 'unknown')}")
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")

if __name__ == "__main__":
    test_422_error()
