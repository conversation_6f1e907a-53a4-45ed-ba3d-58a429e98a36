"""
自定义错误处理器
"""

from typing import Any, Dict, List
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException


def format_validation_error(error: RequestValidationError) -> Dict[str, Any]:
    """
    格式化验证错误为用户友好的消息
    """
    errors = []
    field_errors = {}
    
    for err in error.errors():
        loc = err.get("loc", [])
        msg = err.get("msg", "")
        error_type = err.get("type", "")
        
        # 提取字段路径
        field_path = []
        for item in loc:
            if item != "body":
                field_path.append(str(item))
        
        field_name = ".".join(field_path) if field_path else "unknown"
        
        # 转换为中文错误消息
        chinese_msg = translate_error_message(field_name, msg, error_type)
        
        errors.append({
            "field": field_name,
            "message": chinese_msg,
            "type": error_type
        })
        
        # 按字段分组错误
        if field_name not in field_errors:
            field_errors[field_name] = []
        field_errors[field_name].append(chinese_msg)
    
    # 生成总体错误消息
    summary_messages = []
    for field, messages in field_errors.items():
        summary_messages.append(f"{field}: {', '.join(messages)}")
    
    return {
        "detail": f"数据验证失败: {'; '.join(summary_messages)}",
        "errors": errors,
        "field_errors": field_errors
    }


def translate_error_message(field_name: str, msg: str, error_type: str) -> str:
    """
    将英文错误消息转换为中文
    """
    # 字段名称映射
    field_names = {
        "customer_id": "客户ID",
        "salesperson_id": "销售员ID",
        "payment_method": "付款方式",
        "amount_paid": "已付金额",
        "items": "订单明细",
        "rabbit_type_id": "兔子类型ID",
        "quantity": "数量",
        "unit": "单位",
        "unit_price": "单价",
        "remark": "备注",
        "name": "名称",
        "phone": "手机号",
        "address": "地址",
        "username": "用户名",
        "password": "密码",
        "role": "角色"
    }
    
    # 获取中文字段名
    chinese_field = field_names.get(field_name.split('.')[0], field_name)
    
    # 错误类型映射
    if error_type == "missing":
        return f"{chinese_field}不能为空"
    elif error_type == "string_too_short":
        return f"{chinese_field}长度不足"
    elif error_type == "string_too_long":
        return f"{chinese_field}长度超限"
    elif error_type == "value_error":
        if "phone" in field_name.lower():
            return f"{chinese_field}格式不正确"
        return f"{chinese_field}值无效"
    elif error_type == "type_error":
        if "int" in msg:
            return f"{chinese_field}必须是数字"
        elif "float" in msg:
            return f"{chinese_field}必须是数值"
        elif "str" in msg:
            return f"{chinese_field}必须是文本"
        return f"{chinese_field}类型错误"
    elif error_type == "greater_than":
        return f"{chinese_field}必须大于0"
    elif error_type == "less_than_equal":
        return f"{chinese_field}值过大"
    else:
        return f"{chinese_field}: {msg}"


async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """
    处理422验证错误
    """
    formatted_error = format_validation_error(exc)
    
    return JSONResponse(
        status_code=422,
        content=formatted_error
    )


async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    """
    处理HTTP异常
    """
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail}
    )
