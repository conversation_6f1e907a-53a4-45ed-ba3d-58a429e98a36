#!/usr/bin/env python3
"""
简单的权限测试
"""

import requests

BASE_URL = "http://localhost:8000/api/v1"

def simple_test():
    print("🔐 简单权限测试...")
    
    # 1. 登录
    print("\n1. 登录...")
    login_data = {
        "phone": "13800138000",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    if response.status_code == 200:
        token_data = response.json()
        headers = {"Authorization": f"Bearer {token_data['access_token']}"}
        print("✅ 登录成功")
    else:
        print(f"❌ 登录失败: {response.text}")
        return
    
    # 2. 查看我的权限
    print("\n2. 查看我的权限...")
    response = requests.get(f"{BASE_URL}/permissions/my-permissions", headers=headers)
    if response.status_code == 200:
        perms = response.json()
        print(f"✅ 当前角色: {perms['role']['name']}")
        print(f"   权限数量: {perms['total_permissions']}")
    else:
        print(f"❌ 获取权限失败: {response.text}")
    
    # 3. 测试权限检查
    print("\n3. 测试权限检查...")
    response = requests.get(f"{BASE_URL}/permissions/check/user:read", headers=headers)
    if response.status_code == 200:
        check = response.json()
        print(f"✅ 用户读取权限: {'有' if check['has_permission'] else '无'}")
    else:
        print(f"❌ 权限检查失败: {response.text}")
    
    # 4. 测试API访问
    print("\n4. 测试API访问...")
    response = requests.get(f"{BASE_URL}/customers/", headers=headers)
    if response.status_code == 200:
        customers = response.json()
        print(f"✅ 可以访问客户API: {len(customers)} 个客户")
    elif response.status_code == 403:
        print("❌ 无权限访问客户API")
    else:
        print(f"❌ 访问客户API失败: {response.status_code}")
    
    print("\n🎉 简单权限测试完成！")

if __name__ == "__main__":
    simple_test()
