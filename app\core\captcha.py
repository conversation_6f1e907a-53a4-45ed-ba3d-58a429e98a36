"""
验证码生成和验证模块
"""

import random
import string
import io
import base64
from typing import Tuple, Optional
from PIL import Image, ImageDraw, ImageFont
import hashlib
import time
from datetime import datetime, timedelta


class CaptchaGenerator:
    """验证码生成器"""
    
    def __init__(self, width: int = 120, height: int = 40):
        self.width = width
        self.height = height
        self.font_size = 20
        
    def generate_text(self, length: int = 4) -> str:
        """生成随机验证码文本"""
        # 排除容易混淆的字符
        chars = "23456789ABCDEFGHJKLMNPQRSTUVWXYZ"
        return ''.join(random.choice(chars) for _ in range(length))
    
    def create_image(self, text: str) -> Image.Image:
        """创建验证码图片"""
        # 创建图片
        image = Image.new('RGB', (self.width, self.height), color='white')
        draw = ImageDraw.Draw(image)
        
        # 添加背景噪点
        for _ in range(100):
            x = random.randint(0, self.width)
            y = random.randint(0, self.height)
            draw.point((x, y), fill=self._random_color())
        
        # 添加干扰线
        for _ in range(5):
            x1 = random.randint(0, self.width)
            y1 = random.randint(0, self.height)
            x2 = random.randint(0, self.width)
            y2 = random.randint(0, self.height)
            draw.line([(x1, y1), (x2, y2)], fill=self._random_color(), width=1)
        
        # 绘制文字
        try:
            # 尝试使用系统字体
            font = ImageFont.truetype("arial.ttf", self.font_size)
        except:
            # 如果没有找到字体，使用默认字体
            font = ImageFont.load_default()
        
        # 计算文字位置
        text_width = len(text) * (self.font_size - 5)
        text_height = self.font_size
        x = (self.width - text_width) // 2
        y = (self.height - text_height) // 2
        
        # 绘制每个字符，添加随机偏移
        for i, char in enumerate(text):
            char_x = x + i * (self.font_size - 5) + random.randint(-3, 3)
            char_y = y + random.randint(-3, 3)
            draw.text((char_x, char_y), char, fill=self._random_color(), font=font)
        
        return image
    
    def _random_color(self) -> Tuple[int, int, int]:
        """生成随机颜色"""
        return (
            random.randint(0, 150),
            random.randint(0, 150),
            random.randint(0, 150)
        )
    
    def generate_captcha(self) -> Tuple[str, str]:
        """
        生成验证码
        返回: (验证码文本, base64编码的图片)
        """
        text = self.generate_text()
        image = self.create_image(text)
        
        # 转换为base64
        buffer = io.BytesIO()
        image.save(buffer, format='PNG')
        image_base64 = base64.b64encode(buffer.getvalue()).decode()
        
        return text, f"data:image/png;base64,{image_base64}"


class CaptchaManager:
    """验证码管理器"""
    
    def __init__(self, expire_minutes: int = 5):
        self.expire_minutes = expire_minutes
        self.captcha_store = {}  # 简单内存存储，生产环境建议使用Redis
        self.generator = CaptchaGenerator()
    
    def create_captcha(self) -> Tuple[str, str]:
        """
        创建验证码
        返回: (captcha_id, base64_image)
        """
        text, image_base64 = self.generator.generate_captcha()
        
        # 生成唯一ID
        captcha_id = self._generate_captcha_id()
        
        # 存储验证码信息
        self.captcha_store[captcha_id] = {
            'text': text.upper(),
            'created_at': datetime.now(),
            'used': False
        }
        
        # 清理过期验证码
        self._cleanup_expired()
        
        return captcha_id, image_base64
    
    def verify_captcha(self, captcha_id: str, user_input: str) -> bool:
        """
        验证验证码
        """
        if not captcha_id or not user_input:
            return False
        
        captcha_info = self.captcha_store.get(captcha_id)
        if not captcha_info:
            return False
        
        # 检查是否已使用
        if captcha_info['used']:
            return False
        
        # 检查是否过期
        if datetime.now() - captcha_info['created_at'] > timedelta(minutes=self.expire_minutes):
            del self.captcha_store[captcha_id]
            return False
        
        # 验证文本（不区分大小写）
        is_valid = captcha_info['text'].upper() == user_input.upper()
        
        # 标记为已使用（无论验证成功与否）
        captcha_info['used'] = True
        
        return is_valid
    
    def _generate_captcha_id(self) -> str:
        """生成验证码ID"""
        timestamp = str(int(time.time() * 1000))
        random_str = ''.join(random.choices(string.ascii_letters + string.digits, k=8))
        return hashlib.md5(f"{timestamp}{random_str}".encode()).hexdigest()
    
    def _cleanup_expired(self):
        """清理过期的验证码"""
        current_time = datetime.now()
        expired_keys = []
        
        for captcha_id, info in self.captcha_store.items():
            if current_time - info['created_at'] > timedelta(minutes=self.expire_minutes):
                expired_keys.append(captcha_id)
        
        for key in expired_keys:
            del self.captcha_store[key]


class LoginAttemptManager:
    """登录尝试管理器"""
    
    def __init__(self, max_attempts: int = 5, lockout_minutes: int = 15):
        self.max_attempts = max_attempts
        self.lockout_minutes = lockout_minutes
        self.attempt_store = {}  # 简单内存存储，生产环境建议使用Redis
    
    def record_attempt(self, identifier: str, success: bool = False):
        """记录登录尝试"""
        current_time = datetime.now()
        
        if identifier not in self.attempt_store:
            self.attempt_store[identifier] = {
                'attempts': 0,
                'last_attempt': current_time,
                'locked_until': None
            }
        
        attempt_info = self.attempt_store[identifier]
        
        if success:
            # 登录成功，清除记录
            del self.attempt_store[identifier]
        else:
            # 登录失败，增加尝试次数
            attempt_info['attempts'] += 1
            attempt_info['last_attempt'] = current_time
            
            # 如果超过最大尝试次数，锁定账户
            if attempt_info['attempts'] >= self.max_attempts:
                attempt_info['locked_until'] = current_time + timedelta(minutes=self.lockout_minutes)
    
    def is_locked(self, identifier: str) -> Tuple[bool, Optional[datetime]]:
        """
        检查是否被锁定
        返回: (是否锁定, 解锁时间)
        """
        if identifier not in self.attempt_store:
            return False, None
        
        attempt_info = self.attempt_store[identifier]
        locked_until = attempt_info.get('locked_until')
        
        if locked_until and datetime.now() < locked_until:
            return True, locked_until
        elif locked_until and datetime.now() >= locked_until:
            # 锁定时间已过，清除记录
            del self.attempt_store[identifier]
            return False, None
        
        return False, None
    
    def get_remaining_attempts(self, identifier: str) -> int:
        """获取剩余尝试次数"""
        if identifier not in self.attempt_store:
            return self.max_attempts
        
        attempt_info = self.attempt_store[identifier]
        return max(0, self.max_attempts - attempt_info['attempts'])
    
    def cleanup_expired(self):
        """清理过期的记录"""
        current_time = datetime.now()
        expired_keys = []
        
        for identifier, info in self.attempt_store.items():
            # 清理超过24小时的记录
            if current_time - info['last_attempt'] > timedelta(hours=24):
                expired_keys.append(identifier)
        
        for key in expired_keys:
            del self.attempt_store[key]


# 全局实例
captcha_manager = CaptchaManager()
login_attempt_manager = LoginAttemptManager()
